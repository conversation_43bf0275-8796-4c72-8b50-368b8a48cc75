#!/usr/bin/env python3
"""
Créer les tables manquantes critiques pour ORBIS
"""

import asyncio
import asyncpg

DATABASE_URL = "**************************************************************************************************/postgres"


async def create_user_profiles_table():
    """Créer la table user_profiles pour Supabase Auth"""
    print("🔧 Création de la table user_profiles...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY,
                first_name VA<PERSON><PERSON><PERSON>(100),
                last_name <PERSON><PERSON><PERSON><PERSON>(100),
                company VARCHAR(200),
                phone VARCHAR(20),
                role VARCHAR(50) DEFAULT 'user',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        
        print("   ✅ Table user_profiles créée")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def create_project_materials_table():
    """Créer la table project_materials"""
    print("🔧 Création de la table project_materials...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS project_materials (
                id SERIAL PRIMARY KEY,
                project_id INTEGER NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
                material_id INTEGER NOT NULL REFERENCES materials(id) ON DELETE CASCADE,
                quantity DECIMAL(10,2) NOT NULL DEFAULT 0,
                unit_price DECIMAL(10,2) NOT NULL DEFAULT 0,
                total_price DECIMAL(10,2) GENERATED ALWAYS AS (quantity * unit_price) STORED,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                UNIQUE(project_id, material_id)
            );
        """)
        
        print("   ✅ Table project_materials créée")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def test_tables():
    """Tester les tables créées"""
    print("\n🧪 Test des tables créées...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Test user_profiles
        await conn.execute("""
            INSERT INTO user_profiles (id, first_name, last_name, company)
            VALUES ('550e8400-e29b-41d4-a716-446655440000', 'Test', 'User', 'Test Company')
            ON CONFLICT (id) DO NOTHING
        """)
        
        count = await conn.fetchval("SELECT COUNT(*) FROM user_profiles")
        print(f"   ✅ user_profiles: {count} enregistrement(s)")
        
        # Nettoyer le test
        await conn.execute("DELETE FROM user_profiles WHERE id = '550e8400-e29b-41d4-a716-446655440000'")
        
        # Test project_materials
        count = await conn.fetchval("SELECT COUNT(*) FROM project_materials")
        print(f"   ✅ project_materials: {count} enregistrement(s)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur test: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création des Tables Manquantes")
    print("="*50)
    
    success = True
    
    # Créer user_profiles (critique pour l'auth)
    if not await create_user_profiles_table():
        success = False
    
    # Créer project_materials (utile pour la gestion)
    if not await create_project_materials_table():
        success = False
    
    # Tester les tables
    if success:
        if await test_tables():
            print("\n🎉 Tables créées avec succès!")
            print("✅ user_profiles : Prête pour Supabase Auth")
            print("✅ project_materials : Prête pour la gestion des matériaux")
            print("\n💡 Vous pouvez maintenant tester l'authentification complète")
        else:
            success = False
    
    if not success:
        print("\n❌ Échec de la création des tables")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
