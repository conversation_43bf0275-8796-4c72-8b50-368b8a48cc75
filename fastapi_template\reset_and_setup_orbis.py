#!/usr/bin/env python3
"""
Script pour réinitialiser la base de données et créer une configuration ORBIS propre
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
import uuid
from supabase import create_client, Client
from app.core.config import settings

# Configuration
ASYNC_DATABASE_URL = settings.DATABASE_URL  # Use the regular PostgreSQL URL for asyncpg
SUPABASE_URL = settings.SUPABASE_URL
SUPABASE_SERVICE_KEY = settings.SUPABASE_SERVICE_ROLE_KEY


async def clear_all_tables():
    """Vider toutes les tables de la base de données"""
    print("🧹 Nettoyage de toutes les tables...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        # Liste des tables à vider (dans l'ordre pour respecter les contraintes FK)
        tables_to_clear = [
            'time_entries',
            'project_employees', 
            'project_documents',
            'budget_lines',
            'budgets',
            'purchase_order_lines',
            'purchase_orders',
            'quote_lines',
            'quotes',
            'document_versions',
            'documents',
            'price_history',
            'technical_sheets',
            'materials',
            'material_categories',
            'supplier_contacts',
            'suppliers',
            'employee_assignments',
            'employees',
            'projects',
            'company_settings',
            'user_companies',
            'users',
            'companies'
        ]
        
        for table in tables_to_clear:
            try:
                result = await conn.execute(f"DELETE FROM {table}")
                print(f"   ✅ Table {table} vidée")
            except Exception as e:
                print(f"   ⚠️  Erreur lors du nettoyage de {table}: {e}")
        
        print("✅ Nettoyage terminé")
        
    except Exception as e:
        print(f"❌ Erreur lors du nettoyage: {e}")
        raise
    finally:
        await conn.close()


async def create_orbis_company():
    """Créer la société ORBIS"""
    print("🏢 Création de la société ORBIS...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        company_id = await conn.fetchval("""
            INSERT INTO companies (name, code, siret, address, phone, email, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
        """,
        "ORBIS",
        "ORBIS001", 
        "12345678901234",
        "123 Avenue de la Construction, 75001 Paris",
        "***********.89",
        "<EMAIL>",
        True,
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print(f"✅ Société ORBIS créée avec l'ID: {company_id}")
        return company_id
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de la société: {e}")
        raise
    finally:
        await conn.close()


async def create_supabase_admin_user():
    """Créer un utilisateur admin dans Supabase Auth"""
    print("👤 Création de l'utilisateur admin dans Supabase...")
    
    try:
        # Utiliser le client admin avec service role key
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        
        admin_email = "<EMAIL>"
        admin_password = "orbis123!"
        
        # Créer l'utilisateur avec Supabase Auth
        auth_response = supabase.auth.admin.create_user({
            "email": admin_email,
            "password": admin_password,
            "email_confirm": True,  # Confirmer l'email automatiquement
            "user_metadata": {
                "first_name": "Admin",
                "last_name": "ORBIS",
                "role": "ADMIN"
            }
        })
        
        if auth_response.user:
            print(f"✅ Utilisateur Supabase créé: {admin_email}")
            print(f"   User ID: {auth_response.user.id}")
            return auth_response.user.id, admin_email, admin_password
        else:
            raise Exception("Échec de la création de l'utilisateur Supabase")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'utilisateur Supabase: {e}")
        raise


async def create_local_admin_user(company_id: int, supabase_user_id: str, email: str):
    """Créer l'utilisateur admin dans la base de données locale"""
    print("👤 Création de l'utilisateur admin local...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        # Créer l'utilisateur local
        user_id = await conn.fetchval("""
            INSERT INTO users (
                id, supabase_user_id, email, first_name, last_name, 
                role, is_active, is_superuser, is_verified, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            RETURNING id
        """,
        uuid.uuid4(),
        supabase_user_id,
        email,
        "Admin",
        "ORBIS",
        "ADMIN",
        True,
        True,
        True,
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        # Associer l'utilisateur à la société ORBIS
        await conn.execute("""
            INSERT INTO user_companies (user_id, company_id, is_default, created_at)
            VALUES ($1, $2, $3, $4)
        """, user_id, company_id, True, datetime.utcnow())
        
        print(f"✅ Utilisateur admin local créé et associé à ORBIS")
        return user_id
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'utilisateur local: {e}")
        raise
    finally:
        await conn.close()


async def create_company_settings(company_id: int):
    """Créer les paramètres par défaut pour la société"""
    print("⚙️ Création des paramètres de la société...")
    
    conn = await asyncpg.connect(ASYNC_DATABASE_URL, statement_cache_size=0)
    
    try:
        await conn.execute("""
            INSERT INTO company_settings (
                company_id, currency, timezone, date_format, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
        """,
        company_id,
        "EUR",
        "Europe/Paris", 
        "DD/MM/YYYY",
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print("✅ Paramètres de la société créés")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des paramètres: {e}")
        raise
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Réinitialisation et Configuration")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    try:
        # 1. Vider toutes les tables
        await clear_all_tables()
        
        # 2. Créer la société ORBIS
        company_id = await create_orbis_company()
        
        # 3. Créer l'utilisateur admin dans Supabase
        supabase_user_id, admin_email, admin_password = await create_supabase_admin_user()
        
        # 4. Créer l'utilisateur admin local
        user_id = await create_local_admin_user(company_id, supabase_user_id, admin_email)
        
        # 5. Créer les paramètres de la société
        await create_company_settings(company_id)
        
        print("\n" + "="*60)
        print("✅ Configuration ORBIS terminée avec succès!")
        print("\n📋 Résumé:")
        print(f"   - Société: ORBIS (ID: {company_id})")
        print(f"   - Utilisateur admin: {admin_email}")
        print(f"   - Mot de passe: {admin_password}")
        print(f"   - Supabase User ID: {supabase_user_id}")
        
        print("\n🔑 Identifiants de connexion:")
        print(f"   Email: {admin_email}")
        print(f"   Mot de passe: {admin_password}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Erreur durant la configuration: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
