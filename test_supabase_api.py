#!/usr/bin/env python3
"""
Orbis Database Test Script - Using Supabase API
Tests connection and data retrieval using Supabase REST API
"""

import requests
import json
from datetime import datetime
import sys

# Supabase configuration
SUPABASE_URL = "https://dkmyxkkokwuxopahokcd.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRrbXl4a2tva3d1eG9wYWhva2NkIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNDYwNDM1MSwiZXhwIjoyMDUwMTgwMzUxfQ.pjkRd5zwqn6ym-7pFOlBdXJnbvLvLSZbmgxPhb4-S8M"

headers = {
    "apikey": SUPABASE_KEY,
    "Authorization": f"Bearer {SUPABASE_KEY}",
    "Content-Type": "application/json",
    "Prefer": "return=representation"
}

def test_connection():
    """Test Supabase API connection"""
    print("🔗 Testing Supabase API connection...")
    try:
        response = requests.get(f"{SUPABASE_URL}/rest/v1/", headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ Connected successfully to Supabase API")
            return True
        else:
            print(f"❌ Connection failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return False

def check_table_exists(table_name):
    """Check if a table exists by trying to query it"""
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/{table_name}?limit=0",
            headers=headers,
            timeout=10
        )
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Error checking table {table_name}: {e}")
        return False

def create_companies():
    """Create sample companies"""
    print("🏢 Creating sample companies...")
    companies = [
        {
            "id": "550e8400-e29b-41d4-a716-446655440001",
            "name": "Entreprise Martin Construction",
            "address": "123 Rue de la Paix, 75001 Paris",
            "phone": "+33 1 42 86 90 00",
            "email": "<EMAIL>"
        },
        {
            "id": "550e8400-e29b-41d4-a716-446655440002",
            "name": "Société Dupont BTP",
            "address": "456 Avenue des Champs, 69001 Lyon",
            "phone": "+33 4 78 90 12 34",
            "email": "<EMAIL>"
        }
    ]
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/companies",
            headers=headers,
            json=companies,
            timeout=10
        )
        if response.status_code in [200, 201]:
            print("✅ Companies created successfully")
            return True
        else:
            print(f"⚠️  Companies response: {response.status_code} - {response.text}")
            return True  # May already exist
    except Exception as e:
        print(f"❌ Error creating companies: {e}")
        return False

def create_sample_projects():
    """Create sample projects"""
    print("🏗️  Creating sample projects...")
    projects = [
        {
            "id": "660e8400-e29b-41d4-a716-446655440001",
            "company_id": "550e8400-e29b-41d4-a716-446655440001",
            "name": "Rénovation Immeuble Haussmannien",
            "description": "Rénovation complète d'un immeuble de 6 étages dans le 16ème arrondissement",
            "status": "in_progress",
            "budget": 850000.00,
            "start_date": "2024-01-15",
            "end_date": "2024-06-30"
        },
        {
            "id": "660e8400-e29b-41d4-a716-446655440002",
            "company_id": "550e8400-e29b-41d4-a716-446655440001",
            "name": "Construction Villa Moderne",
            "description": "Construction d'une villa contemporaine de 200m² avec piscine",
            "status": "planning",
            "budget": 650000.00,
            "start_date": "2024-03-01",
            "end_date": "2024-12-15"
        },
        {
            "id": "660e8400-e29b-41d4-a716-446655440003",
            "company_id": "550e8400-e29b-41d4-a716-446655440002",
            "name": "Extension Bureau Commercial",
            "description": "Extension de 300m² pour bureau commercial avec parking",
            "status": "completed",
            "budget": 280000.00,
            "start_date": "2023-09-01",
            "end_date": "2024-01-30"
        },
        {
            "id": "660e8400-e29b-41d4-a716-446655440004",
            "company_id": "550e8400-e29b-41d4-a716-446655440002",
            "name": "Réhabilitation École Primaire",
            "description": "Mise aux normes et rénovation énergétique école 12 classes",
            "status": "in_progress",
            "budget": 1200000.00,
            "start_date": "2024-02-01",
            "end_date": "2024-08-31"
        },
        {
            "id": "660e8400-e29b-41d4-a716-446655440005",
            "company_id": "550e8400-e29b-41d4-a716-446655440001",
            "name": "Aménagement Place Publique",
            "description": "Réaménagement place publique avec mobilier urbain et éclairage",
            "status": "on_hold",
            "budget": 450000.00,
            "start_date": "2024-05-01",
            "end_date": "2024-09-30"
        }
    ]
    
    try:
        response = requests.post(
            f"{SUPABASE_URL}/rest/v1/projects",
            headers=headers,
            json=projects,
            timeout=10
        )
        if response.status_code in [200, 201]:
            print("✅ Projects created successfully")
            return True
        else:
            print(f"⚠️  Projects response: {response.status_code} - {response.text}")
            return True  # May already exist
    except Exception as e:
        print(f"❌ Error creating projects: {e}")
        return False

def query_projects():
    """Query and display all projects"""
    print("\n🔍 Querying projects table...")
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/projects?select=*,companies(*)",
            headers=headers,
            timeout=10
        )
        
        if response.status_code != 200:
            print(f"❌ Query failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            return []
        
        projects = response.json()
        
        if not projects:
            print("📭 No projects found in database")
            return []
        
        print(f"\n📊 Found {len(projects)} projects:")
        print("=" * 100)
        
        for i, project in enumerate(projects, 1):
            company_name = project.get('companies', {}).get('name', 'Unknown Company') if project.get('companies') else 'No Company'
            print(f"\n🏗️  PROJECT #{i}")
            print(f"   ID: {project.get('id', 'N/A')}")
            print(f"   Name: {project.get('name', 'N/A')}")
            print(f"   Company: {company_name}")
            print(f"   Status: {project.get('status', 'N/A').upper()}")
            print(f"   Budget: {project.get('budget', 0):,.2f} €")
            print(f"   Start Date: {project.get('start_date', 'N/A')}")
            print(f"   End Date: {project.get('end_date', 'N/A')}")
            desc = project.get('description', 'N/A')
            print(f"   Description: {desc[:80]}{'...' if len(desc) > 80 else ''}")
            print(f"   Created: {project.get('created_at', 'N/A')}")
            print("-" * 80)
        
        return projects
        
    except Exception as e:
        print(f"❌ Error querying projects: {e}")
        return []

def get_table_count(table_name):
    """Get count of records in a table"""
    try:
        response = requests.get(
            f"{SUPABASE_URL}/rest/v1/{table_name}?select=count",
            headers={**headers, "Prefer": "count=exact"},
            timeout=10
        )
        if response.status_code == 200:
            count_header = response.headers.get('content-range', '0')
            if '/' in count_header:
                return int(count_header.split('/')[-1])
        return 0
    except Exception as e:
        print(f"❌ Error counting {table_name}: {e}")
        return 0

def main():
    """Main test function"""
    print("🚀 ORBIS Database Test - Projects Query (Supabase API)")
    print("=" * 60)
    print(f"🕐 Test started at: {datetime.now()}")
    print(f"🎯 Target: {SUPABASE_URL}")
    
    # Test 1: Connection
    if not test_connection():
        print("\n❌ FAILED: Cannot connect to Supabase API")
        sys.exit(1)
    
    # Test 2: Check tables
    print("\n🔍 Checking required tables...")
    tables = ['companies', 'projects', 'employees', 'time_entries']
    table_status = {}
    
    for table in tables:
        exists = check_table_exists(table)
        table_status[table] = exists
        status = "✅ EXISTS" if exists else "❌ MISSING"
        count = get_table_count(table) if exists else 0
        print(f"   {table}: {status} ({count} records)")
    
    # Test 3: Create data if needed
    if table_status.get('companies', False) and table_status.get('projects', False):
        company_count = get_table_count('companies')
        project_count = get_table_count('projects')
        
        if company_count == 0:
            print("\n🏢 Companies table is empty, creating sample data...")
            create_companies()
            
        if project_count == 0:
            print("\n🏗️  Projects table is empty, creating sample data...")
            create_sample_projects()
    else:
        print("\n❌ Required tables don't exist. Please run the schema creation script first.")
        print("\nMissing tables:")
        for table, exists in table_status.items():
            if not exists:
                print(f"   - {table}")
        return
    
    # Test 4: Query projects
    projects = query_projects()
    
    # Summary
    print("\n📈 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ Supabase API Connection: OK")
    print(f"✅ Projects Found: {len(projects)}")
    print(f"✅ Total Budget: {sum(p.get('budget', 0) for p in projects):,.2f} €")
    
    if projects:
        statuses = {}
        for p in projects:
            status = p.get('status', 'unknown')
            statuses[status] = statuses.get(status, 0) + 1
        
        print("\n📊 Project Status Distribution:")
        for status, count in statuses.items():
            print(f"   {status.upper()}: {count} projects")
    
    print(f"\n🕐 Test completed at: {datetime.now()}")
    print("\n🎉 Database test completed successfully!")

if __name__ == "__main__":
    main()