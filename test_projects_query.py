#!/usr/bin/env python3
"""
Orbis Database Test Script - Projects Table Query
Tests connection and data retrieval from the projects table
"""

import psycopg2
import sys
from datetime import datetime
from psycopg2.extras import RealDictCursor
import json

# Database connection parameters
DB_CONFIG = {
    'host': 'db.dkmyxkkokwuxopahokcd.supabase.co',
    'port': 5432,
    'database': 'postgres',
    'user': 'postgres',
    'password': 'E5FACUUHRbaX&'
}

def test_connection():
    """Test database connection"""
    print("🔗 Testing database connection...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ Connected successfully to PostgreSQL: {version[:50]}...")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def check_table_exists(table_name):
    """Check if a table exists"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            );
        """, (table_name,))
        exists = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return exists
    except Exception as e:
        print(f"❌ Error checking table existence: {e}")
        return False

def create_sample_data():
    """Create sample projects if table is empty"""
    print("📝 Creating sample project data...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # First, ensure we have companies
        cursor.execute("""
            INSERT INTO companies (id, name, address, phone, email, created_at)
            VALUES 
                ('550e8400-e29b-41d4-a716-446655440001', 'Entreprise Martin Construction', '123 Rue de la Paix, 75001 Paris', '+33 1 42 86 90 00', '<EMAIL>', NOW()),
                ('550e8400-e29b-41d4-a716-446655440002', 'Société Dupont BTP', '456 Avenue des Champs, 69001 Lyon', '+33 4 78 90 12 34', '<EMAIL>', NOW())
            ON CONFLICT (id) DO NOTHING;
        """)
        
        # Then create sample projects
        cursor.execute("""
            INSERT INTO projects (id, company_id, name, description, status, budget, start_date, end_date, created_at, updated_at)
            VALUES 
                ('660e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Rénovation Immeuble Haussmannien', 'Rénovation complète d\'un immeuble de 6 étages dans le 16ème arrondissement', 'in_progress', 850000.00, '2024-01-15', '2024-06-30', NOW(), NOW()),
                ('660e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'Construction Villa Moderne', 'Construction d\'une villa contemporaine de 200m² avec piscine', 'planning', 650000.00, '2024-03-01', '2024-12-15', NOW(), NOW()),
                ('660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'Extension Bureau Commercial', 'Extension de 300m² pour bureau commercial avec parking', 'completed', 280000.00, '2023-09-01', '2024-01-30', NOW(), NOW()),
                ('660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 'Réhabilitation École Primaire', 'Mise aux normes et rénovation énergétique école 12 classes', 'in_progress', 1200000.00, '2024-02-01', '2024-08-31', NOW(), NOW()),
                ('660e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440001', 'Aménagement Place Publique', 'Réaménagement place publique avec mobilier urbain et éclairage', 'on_hold', 450000.00, '2024-05-01', '2024-09-30', NOW(), NOW())
            ON CONFLICT (id) DO NOTHING;
        """)
        
        conn.commit()
        cursor.close()
        conn.close()
        print("✅ Sample data created successfully")
        return True
    except Exception as e:
        print(f"❌ Error creating sample data: {e}")
        return False

def query_projects():
    """Query and display all projects"""
    print("\n🔍 Querying projects table...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Query projects with company info
        cursor.execute("""
            SELECT 
                p.id,
                p.name,
                p.description,
                p.status,
                p.budget,
                p.start_date,
                p.end_date,
                c.name as company_name,
                p.created_at
            FROM projects p
            JOIN companies c ON p.company_id = c.id
            ORDER BY p.created_at DESC;
        """)
        
        projects = cursor.fetchall()
        
        if not projects:
            print("📭 No projects found in database")
            return []
        
        print(f"\n📊 Found {len(projects)} projects:")
        print("=" * 100)
        
        for i, project in enumerate(projects, 1):
            print(f"\n🏗️  PROJECT #{i}")
            print(f"   ID: {project['id']}")
            print(f"   Name: {project['name']}")
            print(f"   Company: {project['company_name']}")
            print(f"   Status: {project['status'].upper()}")
            print(f"   Budget: {project['budget']:,.2f} €")
            print(f"   Start Date: {project['start_date']}")
            print(f"   End Date: {project['end_date']}")
            print(f"   Description: {project['description'][:80]}{'...' if len(project['description']) > 80 else ''}")
            print(f"   Created: {project['created_at']}")
            print("-" * 80)
        
        cursor.close()
        conn.close()
        return projects
        
    except Exception as e:
        print(f"❌ Error querying projects: {e}")
        return []

def get_table_count(table_name):
    """Get count of records in a table"""
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
        count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return count
    except Exception as e:
        print(f"❌ Error counting {table_name}: {e}")
        return 0

def main():
    """Main test function"""
    print("🚀 ORBIS Database Test - Projects Query")
    print("=" * 50)
    print(f"🕐 Test started at: {datetime.now()}")
    print(f"🎯 Target: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    
    # Test 1: Connection
    if not test_connection():
        print("\n❌ FAILED: Cannot connect to database")
        sys.exit(1)
    
    # Test 2: Check tables
    print("\n🔍 Checking required tables...")
    tables = ['companies', 'projects', 'employees', 'time_entries']
    table_status = {}
    
    for table in tables:
        exists = check_table_exists(table)
        table_status[table] = exists
        status = "✅ EXISTS" if exists else "❌ MISSING"
        count = get_table_count(table) if exists else 0
        print(f"   {table}: {status} ({count} records)")
    
    # Test 3: Create data if needed
    if table_status.get('projects', False):
        project_count = get_table_count('projects')
        if project_count == 0:
            print("\n📝 Projects table is empty, creating sample data...")
            create_sample_data()
    else:
        print("\n❌ Projects table doesn't exist. Please run the schema creation script first.")
        return
    
    # Test 4: Query projects
    projects = query_projects()
    
    # Summary
    print("\n📈 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ Database Connection: OK")
    print(f"✅ Projects Found: {len(projects)}")
    print(f"✅ Total Budget: {sum(p.get('budget', 0) for p in projects):,.2f} €")
    
    if projects:
        statuses = {}
        for p in projects:
            status = p['status']
            statuses[status] = statuses.get(status, 0) + 1
        
        print("\n📊 Project Status Distribution:")
        for status, count in statuses.items():
            print(f"   {status.upper()}: {count} projects")
    
    print(f"\n🕐 Test completed at: {datetime.now()}")
    print("\n🎉 Database test completed successfully!")

if __name__ == "__main__":
    main()