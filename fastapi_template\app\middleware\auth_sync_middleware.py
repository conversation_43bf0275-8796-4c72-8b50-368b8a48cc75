"""
Middleware pour synchroniser les utilisateurs Supabase Auth avec la table users locale
"""

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Optional, Dict, Any
import asyncio
from datetime import datetime

from app.core.supabase_client import supabase_client
from app.db.session import AsyncSessionLocal
from app.models.user import User
from app.models.company import Company
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError
import uuid


class AuthSyncMiddleware(BaseHTTPMiddleware):
    """
    Middleware qui :
    1. Vérifie les tokens Supabase
    2. Synchronise les utilisateurs avec la table locale
    3. Enrichit la requête avec les données utilisateur
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.protected_paths = [
            "/api/v1/companies",
            "/api/v1/projects", 
            "/api/v1/employees",
            "/api/v1/auth/me",
            "/api/v1/materials",
            "/api/v1/suppliers"
        ]
        self.public_paths = [
            "/api/v1/auth/login",
            "/api/v1/auth/register", 
            "/docs",
            "/redoc",
            "/openapi.json",
            "/"
        ]

    async def dispatch(self, request: Request, call_next):
        """Traitement principal du middleware"""
        
        # Vérifier si le path nécessite une authentification
        if not self._requires_auth(request.url.path):
            return await call_next(request)
        
        # Extraire le token
        token = self._extract_token(request)
        if not token:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing authentication token"}
            )
        
        # Vérifier le token avec Supabase
        supabase_user = await self._verify_supabase_token(token)
        if not supabase_user:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Invalid or expired token"}
            )
        
        # Synchroniser avec la table locale
        local_user = await self._sync_user_with_local_db(supabase_user)
        if not local_user:
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "User synchronization failed"}
            )
        
        # Enrichir la requête avec les données utilisateur
        request.state.user = local_user
        request.state.supabase_user = supabase_user
        
        return await call_next(request)

    def _requires_auth(self, path: str) -> bool:
        """Vérifier si le path nécessite une authentification"""
        # Chemins publics
        for public_path in self.public_paths:
            if path.startswith(public_path):
                return False
        
        # Chemins protégés
        for protected_path in self.protected_paths:
            if path.startswith(protected_path):
                return True
        
        # Par défaut, protéger les paths API
        return path.startswith("/api/")

    def _extract_token(self, request: Request) -> Optional[str]:
        """Extraire le token Bearer de l'en-tête Authorization"""
        auth_header = request.headers.get("Authorization")
        if auth_header and auth_header.startswith("Bearer "):
            return auth_header[7:]  # Enlever "Bearer "
        return None

    async def _verify_supabase_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Vérifier le token avec Supabase Auth"""
        try:
            user_data = await supabase_client.verify_jwt_token(token)
            return user_data
        except Exception as e:
            print(f"Erreur vérification token Supabase: {e}")
            return None

    async def _sync_user_with_local_db(self, supabase_user: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Synchroniser l'utilisateur Supabase avec la table locale"""
        try:
            async with AsyncSessionLocal() as session:
                supabase_id = supabase_user.get("id")
                email = supabase_user.get("email")
                
                if not supabase_id or not email:
                    return None
                
                # Chercher l'utilisateur existant par supabase_id ou email
                stmt = select(User).where(
                    (User.supabase_user_id == supabase_id) | 
                    (User.email == email)
                )
                result = await session.execute(stmt)
                existing_user = result.scalar_one_or_none()
                
                if existing_user:
                    # Mettre à jour l'utilisateur existant
                    updated_user = await self._update_existing_user(
                        session, existing_user, supabase_user
                    )
                    return self._user_to_dict(updated_user)
                else:
                    # Créer un nouvel utilisateur
                    new_user = await self._create_new_user(session, supabase_user)
                    return self._user_to_dict(new_user)
                    
        except Exception as e:
            print(f"Erreur synchronisation utilisateur: {e}")
            return None

    async def _update_existing_user(self, session, user: User, supabase_user: Dict[str, Any]) -> User:
        """Mettre à jour un utilisateur existant"""
        # Mettre à jour les champs depuis Supabase
        user.supabase_user_id = supabase_user.get("id")
        user.email = supabase_user.get("email")
        user.updated_at = datetime.utcnow()
        
        # Mettre à jour les métadonnées si disponibles
        user_metadata = supabase_user.get("user_metadata", {})
        if user_metadata.get("first_name"):
            user.first_name = user_metadata["first_name"]
        if user_metadata.get("last_name"):
            user.last_name = user_metadata["last_name"]
        
        await session.commit()
        await session.refresh(user)
        return user

    async def _create_new_user(self, session, supabase_user: Dict[str, Any]) -> User:
        """Créer un nouvel utilisateur local"""
        user_metadata = supabase_user.get("user_metadata", {})
        
        # Créer l'utilisateur avec des valeurs par défaut
        new_user = User(
            id=uuid.uuid4(),
            supabase_user_id=supabase_user.get("id"),
            email=supabase_user.get("email"),
            first_name=user_metadata.get("first_name", ""),
            last_name=user_metadata.get("last_name", ""),
            role="EMPLOYE",  # Rôle par défaut
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        session.add(new_user)
        await session.commit()
        await session.refresh(new_user)
        return new_user

    def _user_to_dict(self, user: User) -> Dict[str, Any]:
        """Convertir un objet User en dictionnaire"""
        return {
            "id": str(user.id),
            "supabase_user_id": user.supabase_user_id,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "role": user.role.value if hasattr(user.role, 'value') else str(user.role),
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }


# Fonction helper pour obtenir l'utilisateur actuel dans les endpoints
def get_current_user(request: Request) -> Optional[Dict[str, Any]]:
    """Obtenir l'utilisateur actuel depuis le middleware"""
    return getattr(request.state, 'user', None)


def get_current_supabase_user(request: Request) -> Optional[Dict[str, Any]]:
    """Obtenir l'utilisateur Supabase actuel depuis le middleware"""
    return getattr(request.state, 'supabase_user', None)


# Dependency pour FastAPI
async def require_auth(request: Request) -> Dict[str, Any]:
    """Dependency FastAPI pour exiger une authentification"""
    user = get_current_user(request)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return user


async def require_admin(request: Request) -> Dict[str, Any]:
    """Dependency FastAPI pour exiger un rôle admin"""
    user = await require_auth(request)
    if user.get("role") != "ADMIN":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return user
