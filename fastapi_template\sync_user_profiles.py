#!/usr/bin/env python3
"""
Script pour synchroniser les données entre users et user_profiles
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings

DATABASE_URL = settings.DATABASE_URL


async def sync_user_profiles():
    """Synchroniser les données entre users et user_profiles"""
    print("🔄 Synchronisation des profils utilisateurs...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # 1. Récupérer tous les utilisateurs de la table users
        print("   📋 Récupération des utilisateurs...")
        users = await conn.fetch("""
            SELECT id, email, first_name, last_name, role, supabase_user_id, 
                   is_active, phone, created_at
            FROM users
        """)
        
        print(f"   📊 {len(users)} utilisateur(s) trouvé(s) dans la table users")
        
        # 2. Vérifier les profils existants
        print("   🔍 Vérification des profils existants...")
        existing_profiles = await conn.fetch("SELECT id FROM user_profiles")
        existing_profile_ids = {str(profile['id']) for profile in existing_profiles}
        
        print(f"   📊 {len(existing_profiles)} profil(s) existant(s) dans user_profiles")
        
        # 3. Synchroniser chaque utilisateur
        for user in users:
            supabase_id = user['supabase_user_id']
            
            if not supabase_id:
                print(f"   ⚠️ Utilisateur {user['email']} n'a pas d'ID Supabase - ignoré")
                continue
            
            print(f"   🔄 Synchronisation de {user['email']}...")
            
            if supabase_id in existing_profile_ids:
                # Mettre à jour le profil existant
                await conn.execute("""
                    UPDATE user_profiles 
                    SET first_name = $1, last_name = $2, company = $3, 
                        role = $4, is_active = $5, updated_at = $6
                    WHERE id = $7
                """, 
                user['first_name'], user['last_name'], 'ORBIS',
                user['role'].lower() if user['role'] else 'user', 
                user['is_active'], datetime.utcnow(), supabase_id
                )
                print(f"     ✅ Profil mis à jour pour {user['email']}")
                
            else:
                # Créer un nouveau profil
                await conn.execute("""
                    INSERT INTO user_profiles (
                        id, first_name, last_name, company, phone, role, 
                        is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
                """,
                supabase_id, user['first_name'], user['last_name'], 'ORBIS',
                user['phone'], user['role'].lower() if user['role'] else 'user',
                user['is_active'], user['created_at'] or datetime.utcnow(), datetime.utcnow()
                )
                print(f"     ✅ Nouveau profil créé pour {user['email']}")
        
        # 4. Vérifier le résultat
        print("\n   📊 Vérification finale...")
        final_profiles = await conn.fetch("""
            SELECT id, first_name, last_name, company, role, is_active
            FROM user_profiles
        """)
        
        print(f"   📋 Profils après synchronisation:")
        for profile in final_profiles:
            print(f"     • {profile['first_name']} {profile['last_name']} ({profile['role']}) - {profile['company']} - Actif: {profile['is_active']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def verify_sync():
    """Vérifier la synchronisation"""
    print("\n🔍 Vérification de la synchronisation...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Joindre les deux tables pour vérifier la cohérence
        sync_check = await conn.fetch("""
            SELECT 
                u.email,
                u.first_name as users_first_name,
                u.last_name as users_last_name,
                u.role as users_role,
                u.is_active as users_active,
                up.first_name as profile_first_name,
                up.last_name as profile_last_name,
                up.role as profile_role,
                up.is_active as profile_active
            FROM users u
            LEFT JOIN user_profiles up ON u.supabase_user_id = up.id::text
            WHERE u.supabase_user_id IS NOT NULL
        """)
        
        print("   📋 Comparaison users vs user_profiles:")
        for row in sync_check:
            users_name = f"{row['users_first_name']} {row['users_last_name']}"
            profile_name = f"{row['profile_first_name']} {row['profile_last_name']}" if row['profile_first_name'] else "N/A"
            
            name_match = "✅" if users_name == profile_name else "❌"
            role_match = "✅" if row['users_role'].lower() == row['profile_role'] else "❌"
            active_match = "✅" if row['users_active'] == row['profile_active'] else "❌"
            
            print(f"     • {row['email']}:")
            print(f"       Nom: {users_name} → {profile_name} {name_match}")
            print(f"       Rôle: {row['users_role']} → {row['profile_role']} {role_match}")
            print(f"       Actif: {row['users_active']} → {row['profile_active']} {active_match}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Synchronisation des Profils Utilisateurs")
    print("="*60)
    
    if await sync_user_profiles():
        if await verify_sync():
            print("\n✅ Synchronisation terminée avec succès!")
            
            print("\n📝 Résumé:")
            print("   • Table 'users' : Données principales des utilisateurs")
            print("   • Table 'user_profiles' : Profils Supabase synchronisés")
            print("   • Lien : users.supabase_user_id = user_profiles.id")
            
            print("\n🎯 Prochaines étapes:")
            print("   1. Utiliser 'users' pour la logique métier")
            print("   2. Utiliser 'user_profiles' pour l'interface Supabase")
            print("   3. Maintenir la synchronisation lors des mises à jour")
            
            return True
    
    print("\n❌ Échec de la synchronisation")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
