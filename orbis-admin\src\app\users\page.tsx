'use client'

import CompleteUsersPage from './complete-page'

export default function UsersPage() {
  return <CompleteUsersPage />
}

  useEffect(() => {
    fetchUsers()
    fetchInvitations()
  }, [])

  const fetchUsers = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/admin/users')
      // const data = await response.json()

      // Mock data for now
      const mockUsers: User[] = [
        {
          id: 1,
          email: "<EMAIL>",
          first_name: "<PERSON><PERSON>",
          last_name: "<PERSON><PERSON><PERSON>",
          role: "admin",
          is_active: true,
          is_verified: true,
          created_at: "2024-01-15T10:00:00Z",
          last_sign_in_at: "2024-12-30T14:30:00Z",
          company_role: "admin"
        },
        {
          id: 2,
          email: "<EMAIL>",
          first_name: "<PERSON>",
          last_name: "<PERSON><PERSON>",
          role: "manager",
          is_active: true,
          is_verified: true,
          created_at: "2024-02-01T14:30:00Z",
          last_sign_in_at: "2024-12-29T09:15:00Z",
          company_role: "manager"
        },
        {
          id: 3,
          email: "<EMAIL>",
          first_name: "Marie",
          last_name: "Martin",
          role: "user",
          is_active: false,
          is_verified: true,
          created_at: "2024-03-15T11:00:00Z",
          last_sign_in_at: "2024-12-20T16:45:00Z",
          company_role: "user"
        }
      ]

      setUsers(mockUsers)
    } catch (error) {
      console.error('Error fetching users:', error)
    }
  }

  const fetchInvitations = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/admin/invitations')
      // const data = await response.json()
      
      // Mock data for now
      const mockInvitations: Invitation[] = [
        {
          id: 1,
          email: "<EMAIL>",
          role: "admin",
          expires_at: "2024-12-31T23:59:59Z",
          is_pending: false,
          is_expired: false,
          accepted_at: "2024-01-15T10:00:00Z",
          invited_by_name: "System Admin",
          created_at: "2024-01-15T09:00:00Z"
        },
        {
          id: 2,
          email: "<EMAIL>",
          role: "manager",
          expires_at: "2024-12-31T23:59:59Z",
          is_pending: true,
          is_expired: false,
          invited_by_name: "Admin ORBIS",
          created_at: "2024-12-20T14:30:00Z"
        },
        {
          id: 3,
          email: "<EMAIL>",
          role: "user",
          expires_at: "2024-12-15T23:59:59Z",
          is_pending: false,
          is_expired: true,
          rejected_at: "2024-12-10T16:00:00Z",
          invited_by_name: "Admin ORBIS",
          created_at: "2024-12-08T11:00:00Z"
        }
      ]
      
      setInvitations(mockInvitations)
    } catch (error) {
      console.error('Error fetching invitations:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInvite = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/admin/invitations', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(inviteForm)
      // })
      
      // Mock success
      const newInvitation: Invitation = {
        id: Date.now(),
        email: inviteForm.email,
        role: inviteForm.role,
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        is_pending: true,
        is_expired: false,
        invited_by_name: "Admin ORBIS",
        created_at: new Date().toISOString()
      }
      
      setInvitations([newInvitation, ...invitations])
      setShowInviteModal(false)
      setInviteForm({ email: '', role: 'user' })
      
      alert('Invitation envoyée avec succès!')
    } catch (error) {
      console.error('Error sending invitation:', error)
      alert('Erreur lors de l\'envoi de l\'invitation')
    }
  }

  const handleCreateUser = async (e: React.FormEvent) => {
    e.preventDefault()

    if (userForm.password !== userForm.confirm_password) {
      alert('Les mots de passe ne correspondent pas')
      return
    }

    try {
      // TODO: Replace with actual API call to create user in Supabase
      // const response = await fetch('/api/admin/users', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(userForm)
      // })

      // Mock success
      const newUser: User = {
        id: Date.now(),
        email: userForm.email,
        first_name: userForm.first_name,
        last_name: userForm.last_name,
        role: userForm.role,
        is_active: true,
        is_verified: true,
        created_at: new Date().toISOString(),
        company_role: userForm.role
      }

      setUsers([newUser, ...users])
      setShowUserModal(false)
      setUserForm({ email: '', first_name: '', last_name: '', role: 'user', password: '', confirm_password: '' })

      alert('Utilisateur créé avec succès!')
    } catch (error) {
      console.error('Error creating user:', error)
      alert('Erreur lors de la création de l\'utilisateur')
    }
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setUserForm({
      email: user.email,
      first_name: user.first_name,
      last_name: user.last_name,
      role: user.role,
      password: '',
      confirm_password: ''
    })
    setShowUserModal(true)
  }

  const handleUpdateUser = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!editingUser) return

    try {
      // TODO: Replace with actual API call
      // const response = await fetch(`/api/admin/users/${editingUser.id}`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(userForm)
      // })

      // Mock success
      const updatedUsers = users.map(user =>
        user.id === editingUser.id
          ? { ...user, ...userForm, id: editingUser.id, created_at: user.created_at }
          : user
      )

      setUsers(updatedUsers)
      setShowUserModal(false)
      setEditingUser(null)
      setUserForm({ email: '', first_name: '', last_name: '', role: 'user', password: '', confirm_password: '' })

      alert('Utilisateur modifié avec succès!')
    } catch (error) {
      console.error('Error updating user:', error)
      alert('Erreur lors de la modification de l\'utilisateur')
    }
  }

  const handleDeleteUser = async (userId: number) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?')) {
      return
    }

    try {
      // TODO: Replace with actual API call
      // await fetch(`/api/admin/users/${userId}`, { method: 'DELETE' })

      // Mock success
      setUsers(users.filter(user => user.id !== userId))

      alert('Utilisateur supprimé avec succès!')
    } catch (error) {
      console.error('Error deleting user:', error)
      alert('Erreur lors de la suppression de l\'utilisateur')
    }
  }

  const handleToggleUserStatus = async (userId: number) => {
    try {
      // TODO: Replace with actual API call
      // await fetch(`/api/admin/users/${userId}/toggle-status`, { method: 'POST' })

      // Mock success
      const updatedUsers = users.map(user =>
        user.id === userId
          ? { ...user, is_active: !user.is_active }
          : user
      )

      setUsers(updatedUsers)

      alert('Statut de l\'utilisateur modifié avec succès!')
    } catch (error) {
      console.error('Error toggling user status:', error)
      alert('Erreur lors de la modification du statut')
    }
  }

  const handleResend = async (invitationId: number) => {
    try {
      // TODO: Replace with actual API call
      // await fetch(`/api/admin/invitations/${invitationId}/resend`, { method: 'POST' })

      alert('Invitation renvoyée avec succès!')
    } catch (error) {
      console.error('Error resending invitation:', error)
      alert('Erreur lors du renvoi de l\'invitation')
    }
  }

  const filteredInvitations = invitations.filter(invitation =>
    invitation.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invitation.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
    invitation.invited_by_name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getStatusBadge = (invitation: Invitation) => {
    if (invitation.accepted_at) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Acceptée
        </span>
      )
    }
    if (invitation.rejected_at) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <XCircle className="w-3 h-3 mr-1" />
          Refusée
        </span>
      )
    }
    if (invitation.is_expired) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <Clock className="w-3 h-3 mr-1" />
          Expirée
        </span>
      )
    }
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <Clock className="w-3 h-3 mr-1" />
        En attente
      </span>
    )
  }

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: 'bg-purple-100 text-purple-800',
      manager: 'bg-blue-100 text-blue-800',
      user: 'bg-gray-100 text-gray-800',
      viewer: 'bg-green-100 text-green-800'
    }
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[role as keyof typeof colors] || colors.user}`}>
        {role.charAt(0).toUpperCase() + role.slice(1)}
      </span>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
              </Link>
              <Users className="h-8 w-8 text-green-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Gestion des Utilisateurs</h1>
            </div>
            <button
              onClick={() => setShowInviteModal(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Mail className="h-4 w-4" />
              Inviter un Utilisateur
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher une invitation..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full max-w-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Invitations Table */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
          </div>
        ) : (
          <div className="bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {filteredInvitations.map((invitation) => (
                <li key={invitation.id} className="px-6 py-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{invitation.email}</p>
                          <p className="text-sm text-gray-500">
                            Invité par {invitation.invited_by_name} • {new Date(invitation.created_at).toLocaleDateString('fr-FR')}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getRoleBadge(invitation.role)}
                          {getStatusBadge(invitation)}
                        </div>
                      </div>
                      {invitation.is_pending && (
                        <p className="text-xs text-gray-500 mt-1">
                          Expire le {new Date(invitation.expires_at).toLocaleDateString('fr-FR')}
                        </p>
                      )}
                    </div>
                    <div className="ml-4 flex items-center space-x-2">
                      {invitation.is_pending && !invitation.is_expired && (
                        <button
                          onClick={() => handleResend(invitation.id)}
                          className="text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-1"
                        >
                          <Send className="h-3 w-3" />
                          Renvoyer
                        </button>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}

        {filteredInvitations.length === 0 && !loading && (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune invitation trouvée</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Essayez de modifier votre recherche.' : 'Commencez par inviter un utilisateur.'}
            </p>
          </div>
        )}
      </main>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4">Inviter un Utilisateur</h3>
            <form onSubmit={handleInvite}>
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  required
                  value={inviteForm.email}
                  onChange={(e) => setInviteForm({ ...inviteForm, email: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rôle
                </label>
                <select
                  value={inviteForm.role}
                  onChange={(e) => setInviteForm({ ...inviteForm, role: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                >
                  <option value="user">Utilisateur</option>
                  <option value="manager">Manager</option>
                  <option value="admin">Administrateur</option>
                  <option value="viewer">Lecteur</option>
                </select>
              </div>
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowInviteModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Annuler
                </button>
                <button
                  type="submit"
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
                >
                  Envoyer l'Invitation
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
}
