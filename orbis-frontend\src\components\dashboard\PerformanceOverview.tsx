import React from 'react'
import { Card } from '@/components/ui/Card'

interface PerformanceMetric {
  label: string
  value: number
  maxValue: number
  color: string
  trend?: number
}

interface PerformanceOverviewProps {
  metrics?: PerformanceMetric[]
  isLoading?: boolean
}

export const PerformanceOverview: React.FC<PerformanceOverviewProps> = ({ 
  metrics, 
  isLoading = false 
}) => {
  const defaultMetrics: PerformanceMetric[] = [
    {
      label: 'Projets Terminés',
      value: 85,
      maxValue: 100,
      color: 'emerald',
      trend: 12
    },
    {
      label: 'Respect des Délais',
      value: 78,
      maxValue: 100,
      color: 'blue',
      trend: -3
    },
    {
      label: 'Satisfaction Client',
      value: 92,
      maxValue: 100,
      color: 'purple',
      trend: 8
    },
    {
      label: 'Efficacité Équipe',
      value: 88,
      maxValue: 100,
      color: 'amber',
      trend: 5
    }
  ]

  const displayMetrics = metrics || defaultMetrics

  const getColorClasses = (color: string) => {
    const colorMap = {
      emerald: {
        bg: 'bg-emerald-500',
        light: 'bg-emerald-100',
        text: 'text-emerald-700'
      },
      blue: {
        bg: 'bg-blue-500',
        light: 'bg-blue-100',
        text: 'text-blue-700'
      },
      purple: {
        bg: 'bg-purple-500',
        light: 'bg-purple-100',
        text: 'text-purple-700'
      },
      amber: {
        bg: 'bg-amber-500',
        light: 'bg-amber-100',
        text: 'text-amber-700'
      }
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <Card className="p-6">
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Performance Globale</h3>
        <p className="text-sm text-gray-500 mt-1">Indicateurs clés de performance</p>
      </div>

      {isLoading ? (
        <div className="space-y-6">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="animate-pulse">
              <div className="flex justify-between items-center mb-2">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="h-4 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="space-y-6">
          {displayMetrics.map((metric, index) => {
            const percentage = (metric.value / metric.maxValue) * 100
            const colors = getColorClasses(metric.color)
            
            return (
              <div 
                key={metric.label} 
                className="animate-fadeIn"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">{metric.label}</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-semibold text-gray-900">
                      {metric.value}%
                    </span>
                    {metric.trend && (
                      <span className={`flex items-center text-xs font-medium ${
                        metric.trend > 0 ? 'text-emerald-600' : 'text-red-600'
                      }`}>
                        {metric.trend > 0 ? (
                          <svg className="w-3 h-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 10l7-7m0 0l7 7m-7-7v18" />
                          </svg>
                        ) : (
                          <svg className="w-3 h-3 mr-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        )}
                        {Math.abs(metric.trend)}%
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="relative">
                  <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
                    <div 
                      className={`h-full ${colors.bg} rounded-full transition-all duration-1000 ease-out`}
                      style={{ 
                        width: `${percentage}%`,
                        animation: `slideIn 1s ease-out ${index * 150}ms both`
                      }}
                    ></div>
                  </div>
                  
                  {/* Glow effect */}
                  <div 
                    className={`absolute top-0 h-full ${colors.light} rounded-full opacity-50 blur-sm transition-all duration-1000 ease-out`}
                    style={{ 
                      width: `${percentage}%`,
                      animation: `slideIn 1s ease-out ${index * 150}ms both`
                    }}
                  ></div>
                </div>
              </div>
            )
          })}
        </div>
      )}
    </Card>
  )
}