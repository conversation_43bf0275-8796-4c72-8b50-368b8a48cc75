#!/usr/bin/env python3
"""
Script pour recréer l'utilisateur admin avec un mot de passe garanti
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import supabase_service

DATABASE_URL = settings.DATABASE_URL


async def recreate_admin_user():
    """Recréer l'utilisateur admin avec un mot de passe garanti"""
    print("🔄 Recréation de l'utilisateur admin...")
    
    admin_email = "<EMAIL>"
    admin_password = "Admin123!"  # Nouveau mot de passe plus sûr
    
    try:
        # 1. Supprimer l'ancien utilisateur Supabase
        print("   🗑️ Suppression de l'ancien utilisateur Supabase...")
        
        users_data = await supabase_service.list_users()
        old_user_id = None
        
        for user in users_data.get("users", []):
            if user.get("email") == admin_email:
                old_user_id = user.get("id")
                success = await supabase_service.delete_user(old_user_id)
                if success:
                    print(f"   ✅ Ancien utilisateur Supabase supprimé: {old_user_id[:8]}...")
                else:
                    print(f"   ⚠️ Échec de suppression, continuons...")
                break
        
        # 2. Supprimer l'ancien utilisateur local
        print("   🗑️ Suppression de l'ancien utilisateur local...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Supprimer les relations user_companies
            await conn.execute("""
                DELETE FROM user_companies 
                WHERE user_id IN (SELECT id FROM users WHERE email = $1)
            """, admin_email)
            
            # Supprimer l'utilisateur
            deleted_count = await conn.fetchval("""
                DELETE FROM users WHERE email = $1 RETURNING id
            """, admin_email)
            
            if deleted_count:
                print(f"   ✅ Ancien utilisateur local supprimé")
            
            # Supprimer le profil user_profiles si il existe
            if old_user_id:
                await conn.execute("DELETE FROM user_profiles WHERE id = $1", old_user_id)
                print(f"   ✅ Ancien profil supprimé")
        
        finally:
            await conn.close()
        
        # 3. Créer le nouvel utilisateur Supabase
        print("   👤 Création du nouvel utilisateur Supabase...")
        
        user_metadata = {
            "first_name": "Admin",
            "last_name": "ORBIS",
            "role": "admin",
            "company": "ORBIS"
        }
        
        new_user = await supabase_service.create_user(
            email=admin_email,
            password=admin_password,
            user_metadata=user_metadata
        )
        
        # Extraire l'ID utilisateur de la réponse
        if "user" in new_user:
            new_user_id = new_user["user"]["id"]
        elif "id" in new_user:
            new_user_id = new_user["id"]
        else:
            # Récupérer l'ID depuis la liste des utilisateurs
            users_data = await supabase_service.list_users()
            for user in users_data.get("users", []):
                if user.get("email") == admin_email:
                    new_user_id = user.get("id")
                    break
        
        print(f"   ✅ Nouvel utilisateur Supabase créé: {new_user_id[:8]}...")
        
        # 4. Créer l'utilisateur local
        print("   🏠 Création de l'utilisateur local...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Récupérer l'entreprise ORBIS
            company = await conn.fetchrow("SELECT id, name FROM companies WHERE name = 'ORBIS'")
            if not company:
                print("   ❌ Entreprise ORBIS non trouvée")
                return False
            
            # Créer l'utilisateur local
            user_id = await conn.fetchval("""
                INSERT INTO users (
                    supabase_user_id, email, first_name, last_name, 
                    role, is_active, is_superuser, is_verified, 
                    created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING id
            """,
            new_user_id, admin_email, "Admin", "ORBIS", "ADMIN",
            True, True, True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Utilisateur local créé (ID: {user_id})")
            
            # Associer à l'entreprise
            await conn.execute("""
                INSERT INTO user_companies (
                    user_id, company_id, role, is_default, is_active,
                    joined_at, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """,
            user_id, company['id'], 'admin', True, True,
            datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Utilisateur associé à ORBIS")
            
            # Créer le profil user_profiles
            await conn.execute("""
                INSERT INTO user_profiles (
                    id, first_name, last_name, company, role, 
                    is_active, created_at, updated_at
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
            """,
            new_user_id, "Admin", "ORBIS", "ORBIS", "admin",
            True, datetime.utcnow(), datetime.utcnow()
            )
            
            print(f"   ✅ Profil user_profiles créé")
        
        finally:
            await conn.close()
        
        return new_user_id, admin_password
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None


async def test_new_credentials(user_id, password):
    """Tester les nouveaux identifiants"""
    print("\n🔐 Test des nouveaux identifiants...")
    
    try:
        # Vérifier que l'utilisateur existe dans Supabase
        users_data = await supabase_service.list_users()
        user_found = False
        
        for user in users_data.get("users", []):
            if user.get("id") == user_id:
                user_found = True
                print(f"   ✅ Utilisateur trouvé dans Supabase")
                print(f"   📧 Email: {user.get('email')}")
                print(f"   📊 Email confirmé: {user.get('email_confirmed_at') is not None}")
                print(f"   🏷️ Métadonnées: {user.get('user_metadata', {})}")
                break
        
        if not user_found:
            print(f"   ❌ Utilisateur non trouvé dans Supabase")
            return False
        
        # Vérifier l'utilisateur local
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            local_user = await conn.fetchrow("""
                SELECT u.*, uc.role as company_role, c.name as company_name
                FROM users u
                JOIN user_companies uc ON u.id = uc.user_id
                JOIN companies c ON uc.company_id = c.id
                WHERE u.supabase_user_id = $1
            """, user_id)
            
            if local_user:
                print(f"   ✅ Utilisateur local trouvé")
                print(f"   👤 Nom: {local_user['first_name']} {local_user['last_name']}")
                print(f"   🏢 Entreprise: {local_user['company_name']} ({local_user['company_role']})")
                print(f"   🔑 Rôle: {local_user['role']}")
            else:
                print(f"   ❌ Utilisateur local non trouvé")
                return False
        
        finally:
            await conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Recréation de l'Utilisateur Admin")
    print("="*60)
    
    result = await recreate_admin_user()
    
    if result[0]:  # user_id
        user_id, password = result
        
        if await test_new_credentials(user_id, password):
            print("\n✅ Utilisateur admin recréé avec succès!")
            
            print("\n🔑 NOUVEAUX IDENTIFIANTS:")
            print("="*40)
            print(f"📧 Email: <EMAIL>")
            print(f"🔑 Mot de passe: {password}")
            print("="*40)
            
            print("\n🎯 Instructions de test:")
            print("   1. Aller sur: http://localhost:3001/login")
            print("   2. Utiliser les nouveaux identifiants ci-dessus")
            print("   3. Si ça ne marche pas, utiliser: http://localhost:3001/dev-login")
            
            print("\n📝 Informations techniques:")
            print(f"   • Supabase User ID: {user_id}")
            print("   • Tables synchronisées: users, user_profiles, user_companies")
            print("   • Entreprise: ORBIS")
            print("   • Rôle: admin")
            
            return True
    
    print("\n❌ Échec de la recréation de l'utilisateur admin")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
