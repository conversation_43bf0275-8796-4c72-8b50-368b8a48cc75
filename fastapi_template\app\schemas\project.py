# app/schemas/project.py
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models.project import ProjectStatus

class ProjectBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[ProjectStatus] = ProjectStatus.DAO
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    budget_total: Optional[Decimal] = None
    address: Optional[str] = None
    client_name: Optional[str] = None
    client_contact: Optional[str] = None
    is_archived: Optional[bool] = False

class ProjectCreate(ProjectBase):
    name: str
    code: str
    company_id: int

class ProjectUpdate(ProjectBase):
    pass

class ProjectInDBBase(ProjectBase):
    id: Optional[int] = None
    company_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Project(ProjectInDBBase):
    pass

class ProjectDocument(BaseModel):
    id: Optional[int] = None
    project_id: int
    document_id: int
    folder_type: Optional[str] = None
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ProjectEmployee(BaseModel):
    id: Optional[int] = None
    project_id: int
    employee_id: int
    role: Optional[str] = None
    hourly_rate: Optional[Decimal] = None
    assigned_at: Optional[datetime] = None
    is_active: Optional[bool] = True

    class Config:
        from_attributes = True