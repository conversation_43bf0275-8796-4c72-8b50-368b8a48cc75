#!/usr/bin/env python3
"""
Script pour vérifier et créer l'utilisateur admin dans Supabase
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import supabase_service

DATABASE_URL = settings.DATABASE_URL


async def verify_and_create_admin():
    """Vérifier et créer l'utilisateur admin dans Supabase"""
    print("🔍 Vérification de l'utilisateur admin dans Supabase...")
    
    admin_email = "<EMAIL>"
    admin_password = "orbis123!"
    
    try:
        # 1. Lister tous les utilisateurs Supabase
        print("   📋 Liste des utilisateurs Supabase:")
        users_data = await supabase_service.list_users()
        users = users_data.get("users", [])
        
        print(f"   📊 {len(users)} utilisateur(s) trouvé(s)")
        
        admin_user = None
        for user in users:
            email = user.get("email", "")
            user_id = user.get("id", "")
            created_at = user.get("created_at", "")
            print(f"     • {email} (ID: {user_id[:8]}...) - Créé: {created_at[:10]}")
            
            if email == admin_email:
                admin_user = user
        
        if admin_user:
            print(f"\n   ✅ Utilisateur admin trouvé: {admin_user['id']}")
            
            # Vérifier les métadonnées
            metadata = admin_user.get("user_metadata", {})
            print(f"   📋 Métadonnées: {metadata}")
            
            # Mettre à jour les métadonnées si nécessaire
            if not metadata.get("role"):
                print("   🔄 Mise à jour des métadonnées...")
                await supabase_service.update_user(
                    admin_user["id"],
                    user_metadata={
                        "first_name": "Admin",
                        "last_name": "ORBIS",
                        "role": "admin",
                        "company": "ORBIS"
                    }
                )
                print("   ✅ Métadonnées mises à jour")
            
        else:
            print(f"\n   ❌ Utilisateur admin non trouvé, création...")
            
            user_metadata = {
                "first_name": "Admin",
                "last_name": "ORBIS",
                "role": "admin",
                "company": "ORBIS"
            }
            
            try:
                supabase_user = await supabase_service.create_user(
                    email=admin_email,
                    password=admin_password,
                    user_metadata=user_metadata
                )
                
                print(f"   ✅ Utilisateur admin créé dans Supabase")
                admin_user = supabase_user
                
            except Exception as e:
                if "email_exists" in str(e):
                    print("   ⚠️ L'utilisateur existe déjà mais n'apparaît pas dans la liste")
                    # Essayer de récupérer l'utilisateur par email
                    users_data = await supabase_service.list_users()
                    for user in users_data.get("users", []):
                        if user.get("email") == admin_email:
                            admin_user = user
                            break
                else:
                    raise e
        
        # 2. Vérifier la base de données locale
        print("\n   🗄️ Vérification de la base de données locale...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Récupérer l'entreprise ORBIS
            company = await conn.fetchrow("SELECT id, name FROM companies WHERE name = 'ORBIS'")
            if not company:
                print("   ❌ Entreprise ORBIS non trouvée")
                return False
            
            # Vérifier l'utilisateur local
            local_user = await conn.fetchrow(
                "SELECT id, supabase_user_id, email FROM users WHERE email = $1", 
                admin_email
            )
            
            if local_user:
                print(f"   ✅ Utilisateur local trouvé: {local_user['email']}")
                
                # Mettre à jour l'ID Supabase si nécessaire
                if admin_user and local_user['supabase_user_id'] != admin_user['id']:
                    await conn.execute("""
                        UPDATE users 
                        SET supabase_user_id = $1, updated_at = $2
                        WHERE email = $3
                    """, admin_user['id'], datetime.utcnow(), admin_email)
                    print("   🔄 ID Supabase mis à jour dans la base locale")
                
            else:
                print("   ❌ Utilisateur local non trouvé, création...")
                
                if not admin_user:
                    print("   ❌ Impossible de créer l'utilisateur local sans ID Supabase")
                    return False
                
                # Créer l'utilisateur local
                user_id = await conn.fetchval("""
                    INSERT INTO users (
                        supabase_user_id, email, first_name, last_name, 
                        role, is_active, is_superuser, is_verified, 
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    RETURNING id
                """,
                admin_user['id'], admin_email, "Admin", "ORBIS", "ADMIN",
                True, True, True, datetime.utcnow(), datetime.utcnow()
                )
                
                # Associer à l'entreprise
                await conn.execute("""
                    INSERT INTO user_companies (
                        user_id, company_id, role, is_default, is_active,
                        joined_at, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                user_id, company['id'], 'admin', True, True,
                datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
                )
                
                print("   ✅ Utilisateur local créé et associé à ORBIS")
        
        finally:
            await conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


async def test_login():
    """Tester la connexion avec les identifiants"""
    print("\n🔐 Test de connexion...")
    
    admin_email = "<EMAIL>"
    admin_password = "orbis123!"
    
    try:
        # Simuler une connexion (sans utiliser le service car il nécessite une vraie connexion)
        print(f"   📧 Email: {admin_email}")
        print(f"   🔑 Mot de passe: {admin_password}")
        print("   ✅ Identifiants configurés")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur de test: {e}")
        return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Vérification de l'utilisateur admin Supabase")
    print("="*60)
    
    if await verify_and_create_admin():
        print("\n✅ Utilisateur admin vérifié/créé avec succès!")
        
        if await test_login():
            print("\n🎯 Instructions de connexion:")
            print("   1. Ouvrir: http://localhost:3001/login")
            print("   2. Email: <EMAIL>")
            print("   3. Mot de passe: orbis123!")
            
            print("\n🔧 Si la connexion échoue encore:")
            print("   1. Vérifier que le serveur Next.js fonctionne (port 3001)")
            print("   2. Vérifier la console du navigateur pour plus de détails")
            print("   3. Vérifier les variables d'environnement Supabase")
        
        return True
    else:
        print("\n❌ Échec de la vérification de l'utilisateur admin")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
