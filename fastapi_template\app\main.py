# app/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.core.config import settings
from app.api.api_v1.api import api_router
from app.core.database import engine
from app.models import *
from app.middleware.auth_sync_middleware import AuthSyncMiddleware

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    description="ORBIS Suivi Travaux - Construction Project Management SAAS Application",
    version="1.0.0"
)

# Set up CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Auth Sync Middleware
app.add_middleware(AuthSyncMiddleware)

app.include_router(api_router, prefix=settings.API_V1_STR)

@app.get("/")
async def root():
    return {
        "message": "Welcome to ORBIS Suivi Travaux SAAS!",
        "description": "Construction Project Management Platform",
        "version": "1.0.0",
        "docs_url": "/docs",
        "api_version": settings.API_V1_STR
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "ORBIS Suivi Travaux API",
        "version": "1.0.0"
    }

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize Supabase connection on startup"""
    print("✓ ORBIS Suivi Travaux API started with Supabase")
    print("✓ Authentication system ready")

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up on shutdown"""
    print("✓ ORBIS Suivi Travaux API shutdown")