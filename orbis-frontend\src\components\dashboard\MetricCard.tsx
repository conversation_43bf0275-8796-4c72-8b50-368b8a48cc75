import React from 'react'
import { Card } from '@/components/ui/Card'

interface MetricCardProps {
  title: string
  value: string | number
  icon: React.ReactNode
  trend?: {
    value: number
    isPositive: boolean
    label?: string
  }
  gradient: string
  isLoading?: boolean
}

export const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  trend,
  gradient,
  isLoading = false
}) => {
  return (
    <Card className="relative overflow-hidden group hover:scale-[1.02] transition-all duration-300 border-0 shadow-lg hover:shadow-xl">
      {isLoading ? (
        <div className="animate-pulse p-6">
          <div className="flex items-center justify-between">
            <div className="h-12 w-12 rounded-xl bg-gray-200"></div>
            <div className="h-6 w-20 bg-gray-200 rounded"></div>
          </div>
          <div className="mt-4 h-8 bg-gray-200 rounded w-2/3"></div>
          <div className="mt-2 h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      ) : (
        <>
          {/* Background gradient */}
          <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 group-hover:opacity-10 transition-opacity`}></div>
          
          {/* Content */}
          <div className="relative z-10 p-6">
            <div className="flex items-center justify-between">
              {/* Icon */}
              <div className={`p-3 rounded-xl bg-gradient-to-br ${gradient} shadow-lg group-hover:scale-110 transition-transform`}>
                <div className="text-white">
                  {icon}
                </div>
              </div>
              
              {/* Trend */}
              {trend && (
                <div className={`flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
                  trend.isPositive 
                    ? 'bg-emerald-50 text-emerald-700 border border-emerald-200' 
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  {trend.isPositive ? (
                    <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  ) : (
                    <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                  )}
                  {trend.isPositive ? '+' : ''}{trend.value}%
                </div>
              )}
            </div>
            
            {/* Value and Title */}
            <div className="mt-4">
              <div className="text-3xl font-bold text-gray-900 mb-1 group-hover:text-gray-800 transition-colors">
                {value}
              </div>
              <div className="text-sm font-medium text-gray-600">
                {title}
              </div>
              {trend?.label && (
                <div className="text-xs text-gray-500 mt-1">
                  {trend.label}
                </div>
              )}
            </div>
          </div>
          
          {/* Bottom accent */}
          <div className={`h-1 w-full bg-gradient-to-r ${gradient} group-hover:h-1.5 transition-all`}></div>
        </>
      )}
    </Card>
  )
}