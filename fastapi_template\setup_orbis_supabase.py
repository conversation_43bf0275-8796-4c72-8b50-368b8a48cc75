#!/usr/bin/env python3
"""
Script pour configurer ORBIS avec Supabase Auth
Crée seulement la société et les données métier - l'auth est gérée par Supabase
"""

import asyncio
import asyncpg
from datetime import datetime
import sys
from supabase import create_client, Client
from app.core.config import settings

# Configuration
DATABASE_URL = settings.DATABASE_URL
SUPABASE_URL = settings.SUPABASE_URL
SUPABASE_SERVICE_KEY = settings.SUPABASE_SERVICE_ROLE_KEY


async def create_orbis_company():
    """Créer la société ORBIS dans la base de données"""
    print("🏢 Création de la société ORBIS...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si ORBIS existe déjà
        existing = await conn.fetchval("SELECT id FROM companies WHERE name = 'ORBIS'")
        if existing:
            print(f"   ✅ Société ORBIS existe déjà (ID: {existing})")
            return existing
        
        # Créer la société ORBIS
        company_id = await conn.fetchval("""
            INSERT INTO companies (name, code, siret, address, phone, email, is_active, created_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
            RETURNING id
        """,
        "ORBIS",
        "ORBIS001", 
        "12345678901234",
        "123 Avenue de la Construction, 75001 Paris",
        "***********.89",
        "<EMAIL>",
        True,
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print(f"   ✅ Société ORBIS créée avec l'ID: {company_id}")
        return company_id
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de la société: {e}")
        raise
    finally:
        await conn.close()


async def create_company_settings(company_id: int):
    """Créer les paramètres par défaut pour la société"""
    print("⚙️ Création des paramètres de la société...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si les paramètres existent déjà
        existing = await conn.fetchval("SELECT id FROM company_settings WHERE company_id = $1", company_id)
        if existing:
            print("   ✅ Paramètres existent déjà")
            return
        
        await conn.execute("""
            INSERT INTO company_settings (
                company_id, currency, timezone, date_format, 
                created_at, updated_at
            )
            VALUES ($1, $2, $3, $4, $5, $6)
        """,
        company_id,
        "EUR",
        "Europe/Paris", 
        "DD/MM/YYYY",
        datetime.utcnow(),
        datetime.utcnow()
        )
        
        print("   ✅ Paramètres de la société créés")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des paramètres: {e}")
        raise
    finally:
        await conn.close()


async def create_sample_projects(company_id: int):
    """Créer quelques projets d'exemple"""
    print("🏗️ Création de projets d'exemple...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier si des projets existent déjà
        existing_count = await conn.fetchval("SELECT COUNT(*) FROM projects WHERE company_id = $1", company_id)
        if existing_count > 0:
            print(f"   ✅ {existing_count} projets existent déjà")
            return
        
        projects_data = [
            ("Rénovation Bureau Central", "ORBIS-2024-001", "Rénovation complète des bureaux centraux", "en_cours", 150000.00, "Entreprise ABC"),
            ("Construction Entrepôt", "ORBIS-2024-002", "Construction d'un nouvel entrepôt logistique", "en_cours", 300000.00, "Logistique XYZ"),
            ("Aménagement Showroom", "ORBIS-2024-003", "Aménagement d'un showroom moderne", "termine", 80000.00, "Retail Pro")
        ]
        
        for name, code, description, status, budget, client in projects_data:
            project_id = await conn.fetchval("""
                INSERT INTO projects (
                    name, code, description, status, budget_total, company_id, 
                    start_date, created_at, updated_at, client_name
                )
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                RETURNING id
            """,
            name, code, description, status, budget, company_id,
            datetime.utcnow(), datetime.utcnow(), datetime.utcnow(), client
            )
            print(f"   ✅ Projet créé: {name}")
        
    except Exception as e:
        print(f"❌ Erreur lors de la création des projets: {e}")
        raise
    finally:
        await conn.close()


def create_supabase_admin_user():
    """Créer un utilisateur admin dans Supabase Auth"""
    print("👤 Création de l'utilisateur admin dans Supabase Auth...")
    
    try:
        # Utiliser le client admin avec service role key
        supabase: Client = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
        
        admin_email = "<EMAIL>"
        admin_password = "orbis123!"
        
        # Vérifier si l'utilisateur existe déjà
        try:
            existing_users = supabase.auth.admin.list_users()
            for user in existing_users:
                if user.email == admin_email:
                    print(f"   ✅ Utilisateur {admin_email} existe déjà")
                    print(f"   User ID: {user.id}")
                    return user.id, admin_email, admin_password
        except:
            pass
        
        # Créer l'utilisateur avec Supabase Auth
        auth_response = supabase.auth.admin.create_user({
            "email": admin_email,
            "password": admin_password,
            "email_confirm": True,  # Confirmer l'email automatiquement
            "user_metadata": {
                "first_name": "Admin",
                "last_name": "ORBIS",
                "role": "admin",
                "company": "ORBIS"
            }
        })
        
        if auth_response.user:
            print(f"   ✅ Utilisateur Supabase créé: {admin_email}")
            print(f"   User ID: {auth_response.user.id}")
            return auth_response.user.id, admin_email, admin_password
        else:
            raise Exception("Échec de la création de l'utilisateur Supabase")
            
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'utilisateur Supabase: {e}")
        print("   💡 Vous pouvez créer l'utilisateur manuellement dans Supabase Dashboard")
        return None, "<EMAIL>", "orbis123!"


async def verify_setup():
    """Vérifier la configuration créée"""
    print("🔍 Vérification de la configuration...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Vérifier les données créées
        company_count = await conn.fetchval("SELECT COUNT(*) FROM companies WHERE name = 'ORBIS'")
        project_count = await conn.fetchval("SELECT COUNT(*) FROM projects")
        settings_count = await conn.fetchval("SELECT COUNT(*) FROM company_settings")
        
        print(f"   📊 Sociétés ORBIS: {company_count}")
        print(f"   🏗️ Projets: {project_count}")
        print(f"   ⚙️ Paramètres: {settings_count}")
        
        if company_count >= 1 and project_count >= 3:
            print("   ✅ Configuration vérifiée avec succès!")
            return True
        else:
            print("   ❌ Problème dans la configuration")
            return False
        
    except Exception as e:
        print(f"❌ Erreur lors de la vérification: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Configuration avec Supabase Auth")
    print(f"🕐 Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    try:
        # 1. Créer la société ORBIS
        company_id = await create_orbis_company()
        
        # 2. Créer les paramètres de la société
        await create_company_settings(company_id)
        
        # 3. Créer des projets d'exemple
        await create_sample_projects(company_id)
        
        # 4. Créer l'utilisateur admin dans Supabase
        user_id, admin_email, admin_password = create_supabase_admin_user()
        
        # 5. Vérifier la configuration
        if await verify_setup():
            print("\n" + "="*60)
            print("✅ Configuration ORBIS terminée avec succès!")
            print("\n📋 Résumé:")
            print(f"   - Société: ORBIS (ID: {company_id})")
            print(f"   - Utilisateur admin: {admin_email}")
            if user_id:
                print(f"   - Supabase User ID: {user_id}")
            
            print("\n🔑 Identifiants de connexion:")
            print(f"   Email: {admin_email}")
            print(f"   Mot de passe: {admin_password}")
            
            print("\n📝 Prochaines étapes:")
            print("   1. Ouvrir votre application frontend")
            print("   2. Aller sur la page de connexion")
            print("   3. Se connecter avec les identifiants ci-dessus")
            print("   4. Supabase Auth gérera automatiquement l'authentification")
            
            print("\n💡 Architecture:")
            print("   Frontend → Supabase Auth → FastAPI Backend → Supabase DB")
            
            return True
        else:
            print("\n❌ Échec de la vérification")
            return False
        
    except Exception as e:
        print(f"\n❌ Erreur durant la configuration: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
