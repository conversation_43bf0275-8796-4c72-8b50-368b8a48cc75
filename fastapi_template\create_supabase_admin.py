#!/usr/bin/env python3
"""
Script pour créer un utilisateur admin dans Supabase Auth
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import supabase_service

DATABASE_URL = settings.DATABASE_URL


async def create_supabase_admin():
    """Créer un utilisateur admin dans Supabase Auth et la base de données locale"""
    print("👤 Création d'un utilisateur admin dans Supabase...")
    
    admin_email = "<EMAIL>"
    admin_password = "orbis123!"
    
    try:
        # 1. Vérifier si l'utilisateur existe déjà dans Supabase
        print("   🔍 Vérification de l'existence de l'utilisateur...")

        users_data = await supabase_service.list_users()
        existing_user = None

        for user in users_data.get("users", []):
            if user.get("email") == admin_email:
                existing_user = user
                break

        if existing_user:
            supabase_user_id = existing_user["id"]
            print(f"   ✅ Utilisateur Supabase existe déjà: {supabase_user_id}")
        else:
            # Créer l'utilisateur dans Supabase Auth
            print("   🔐 Création dans Supabase Auth...")

            user_metadata = {
                "first_name": "Admin",
                "last_name": "ORBIS",
                "role": "admin",
                "company": "ORBIS"
            }

            supabase_user = await supabase_service.create_user(
                email=admin_email,
                password=admin_password,
                user_metadata=user_metadata
            )

            # La structure de réponse peut varier
            if "user" in supabase_user:
                supabase_user_id = supabase_user["user"]["id"]
            elif "id" in supabase_user:
                supabase_user_id = supabase_user["id"]
            else:
                print(f"   ⚠️ Structure de réponse inattendue: {supabase_user}")
                # Essayer de trouver l'ID dans la réponse
                supabase_user_id = str(supabase_user.get("id", supabase_user))
            print(f"   ✅ Utilisateur Supabase créé: {supabase_user_id}")
        
        # 2. Mettre à jour l'utilisateur local avec l'ID Supabase
        print("   📊 Mise à jour de l'utilisateur local...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Récupérer l'entreprise ORBIS
            company = await conn.fetchrow("SELECT id, name FROM companies WHERE name = 'ORBIS'")
            if not company:
                print("   ❌ Entreprise ORBIS non trouvée")
                return False
            
            # Vérifier si l'utilisateur local existe
            existing_user = await conn.fetchrow(
                "SELECT id FROM users WHERE email = $1", admin_email
            )
            
            if existing_user:
                # Mettre à jour avec l'ID Supabase
                await conn.execute("""
                    UPDATE users 
                    SET supabase_user_id = $1, updated_at = $2
                    WHERE email = $3
                """, supabase_user_id, datetime.utcnow(), admin_email)
                
                print(f"   ✅ Utilisateur local mis à jour avec l'ID Supabase")
            else:
                # Créer l'utilisateur local
                user_id = await conn.fetchval("""
                    INSERT INTO users (
                        supabase_user_id, email, first_name, last_name, 
                        role, is_active, is_superuser, is_verified, 
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    RETURNING id
                """,
                supabase_user_id, admin_email, "Admin", "ORBIS", "ADMIN",
                True, True, True, datetime.utcnow(), datetime.utcnow()
                )
                
                # Associer à l'entreprise
                await conn.execute("""
                    INSERT INTO user_companies (
                        user_id, company_id, role, is_default, is_active,
                        joined_at, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                user_id, company['id'], 'admin', True, True,
                datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
                )
                
                print(f"   ✅ Utilisateur local créé et associé à ORBIS")
        
        finally:
            await conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


async def test_supabase_connection():
    """Tester la connexion à Supabase"""
    print("🔍 Test de la connexion Supabase...")
    
    try:
        # Tester en listant les utilisateurs
        users_data = await supabase_service.list_users(page=1, per_page=5)
        user_count = len(users_data.get("users", []))
        
        print(f"   ✅ Connexion Supabase OK - {user_count} utilisateur(s) trouvé(s)")
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur de connexion Supabase: {e}")
        return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Création d'un utilisateur admin Supabase")
    print("="*60)
    
    # Test de connexion
    if not await test_supabase_connection():
        print("\n❌ Impossible de se connecter à Supabase")
        print("Vérifiez vos variables d'environnement:")
        print("- SUPABASE_URL")
        print("- SUPABASE_SERVICE_ROLE_KEY")
        return False
    
    # Création de l'admin
    if await create_supabase_admin():
        print("\n✅ Utilisateur admin Supabase créé avec succès!")
        print("\n🔑 Identifiants de connexion:")
        print("   Email: <EMAIL>")
        print("   Mot de passe: orbis123!")
        
        print("\n📝 Prochaines étapes:")
        print("   1. Tester la connexion sur l'Admin Portal: http://localhost:3001/login")
        print("   2. Vérifier l'authentification")
        print("   3. Tester les fonctionnalités d'invitation")
        
        return True
    else:
        print("\n❌ Échec de la création de l'utilisateur admin")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
