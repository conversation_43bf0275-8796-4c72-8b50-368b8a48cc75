# app/models/project.py
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, DateTime, ForeignKey, Text, Enum, Numeric
from sqlalchemy.orm import relationship
from datetime import datetime
import enum
from app.core.database import Base

class ProjectStatus(str, enum.Enum):
    DAO = "dao"  # Design-Build
    EXE = "exe"  # Execution
    COMPLETED = "completed"
    ARCHIVED = "archived"

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    name = Column(String, nullable=False, index=True)
    code = Column(String, nullable=False, index=True)
    description = Column(Text)
    status = Column(Enum(ProjectStatus), default=ProjectStatus.DAO)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    budget_total = Column(Numeric(15, 2))
    address = Column(Text)
    client_name = Column(String)
    client_contact = Column(String)
    is_archived = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    company = relationship("Company", back_populates="projects")
    documents = relationship("ProjectDocument", back_populates="project")
    employees = relationship("ProjectEmployee", back_populates="project")
    budgets = relationship("Budget", back_populates="project")
    purchase_orders = relationship("PurchaseOrder", back_populates="project")
    quotes = relationship("Quote", back_populates="project")
    time_entries = relationship("TimeEntry", back_populates="project")

class ProjectDocument(Base):
    __tablename__ = "project_documents"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    document_id = Column(Integer, ForeignKey("documents.id"), nullable=False)
    folder_type = Column(String)  # DCE, DAO, DPGF, FT, EXE, etc.
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    project = relationship("Project", back_populates="documents")
    document = relationship("Document")

class ProjectEmployee(Base):
    __tablename__ = "project_employees"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(Integer, ForeignKey("projects.id"), nullable=False)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    role = Column(String)
    hourly_rate = Column(Numeric(10, 2))
    assigned_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)

    # Relationships
    project = relationship("Project", back_populates="employees")
    employee = relationship("Employee", back_populates="project_assignments")