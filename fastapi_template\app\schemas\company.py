# app/schemas/company.py
from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class CompanyBase(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    address: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    siret: Optional[str] = None
    is_active: Optional[bool] = True

class CompanyCreate(CompanyBase):
    name: str
    code: str

class CompanyUpdate(CompanyBase):
    pass

class CompanyInDBBase(CompanyBase):
    id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class Company(CompanyInDBBase):
    pass

class UserCompany(BaseModel):
    id: Optional[int] = None
    user_id: int
    company_id: int
    is_default: Optional[bool] = False
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class CompanySettings(BaseModel):
    id: Optional[int] = None
    company_id: int
    default_currency: Optional[str] = "EUR"
    date_format: Optional[str] = "DD/MM/YYYY"
    time_format: Optional[str] = "24h"
    language: Optional[str] = "fr"
    logo_url: Optional[str] = None

    class Config:
        from_attributes = True