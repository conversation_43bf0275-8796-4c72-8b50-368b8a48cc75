'use client'

import { ReactNode } from 'react'

interface ModernStatsCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon?: ReactNode
  trend?: {
    value: number
    isPositive: boolean
  }
  gradient: 'blue' | 'purple' | 'orange' | 'green' | 'pink' | 'indigo'
  className?: string
}

const gradientClasses = {
  blue: 'bg-gradient-to-br from-blue-500 to-blue-600',
  purple: 'bg-gradient-to-br from-purple-500 to-purple-600',
  orange: 'bg-gradient-to-br from-orange-500 to-red-500',
  green: 'bg-gradient-to-br from-green-500 to-emerald-600',
  pink: 'bg-gradient-to-br from-pink-500 to-rose-500',
  indigo: 'bg-gradient-to-br from-indigo-500 to-purple-600',
}

export default function ModernStatsCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  gradient,
  className = ''
}: ModernStatsCardProps) {
  return (
    <div className={`
      relative overflow-hidden rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1
      ${gradientClasses[gradient]} text-white p-6 ${className}
    `}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -right-4 -top-4 w-24 h-24 rounded-full bg-white"></div>
        <div className="absolute -right-8 -bottom-8 w-32 h-32 rounded-full bg-white"></div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            {icon && (
              <div className="p-2 bg-white bg-opacity-20 rounded-lg">
                {icon}
              </div>
            )}
            <h3 className="text-sm font-medium text-white opacity-90">{title}</h3>
          </div>
          {trend && (
            <div className={`
              flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium
              ${trend.isPositive 
                ? 'bg-green-500 bg-opacity-20 text-green-100' 
                : 'bg-red-500 bg-opacity-20 text-red-100'
              }
            `}>
              <span>{trend.isPositive ? '↗' : '↘'}</span>
              <span>{Math.abs(trend.value)}%</span>
            </div>
          )}
        </div>

        {/* Value */}
        <div className="mb-2">
          <div className="text-3xl font-bold text-white mb-1">
            {typeof value === 'number' ? value.toLocaleString() : value}
          </div>
          {subtitle && (
            <div className="text-sm text-white opacity-75">
              {subtitle}
            </div>
          )}
        </div>

        {/* Progress Bar (optional) */}
        {trend && (
          <div className="mt-4">
            <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
              <div 
                className="bg-white rounded-full h-2 transition-all duration-500"
                style={{ width: `${Math.min(Math.abs(trend.value), 100)}%` }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

// Composant pour les cartes de statistiques avec icônes spécifiques
export function ProjectStatsCard({ totalProjects, activeProjects }: { totalProjects: number, activeProjects: number }) {
  return (
    <ModernStatsCard
      title="Projets Totaux"
      value={totalProjects}
      subtitle={`${activeProjects} actifs`}
      gradient="blue"
      trend={{ value: 12, isPositive: true }}
      icon={
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
        </svg>
      }
    />
  )
}

export function EmployeeStatsCard({ totalEmployees }: { totalEmployees: number }) {
  return (
    <ModernStatsCard
      title="Équipe"
      value={totalEmployees}
      subtitle="employés actifs"
      gradient="purple"
      trend={{ value: 8, isPositive: true }}
      icon={
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      }
    />
  )
}

export function HoursStatsCard({ totalHours }: { totalHours: number }) {
  return (
    <ModernStatsCard
      title="Heures Travaillées"
      value={`${totalHours.toFixed(0)}h`}
      subtitle="ce mois"
      gradient="green"
      trend={{ value: 15, isPositive: true }}
      icon={
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      }
    />
  )
}

export function TasksStatsCard({ pendingTasks }: { pendingTasks: number }) {
  return (
    <ModernStatsCard
      title="Tâches en Attente"
      value={pendingTasks}
      subtitle="à traiter"
      gradient="orange"
      trend={{ value: 5, isPositive: false }}
      icon={
        <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      }
    />
  )
}
