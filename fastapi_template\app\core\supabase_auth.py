# app/core/supabase_auth.py
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from app.core.supabase_client import supabase_client
import re

class SupabaseAuth:
    def __init__(self):
        self.client = supabase_client

    def validate_password(self, password: str) -> bool:
        """Validate password strength"""
        if len(password) < 8:
            return False
        if not re.search(r'[A-Z]', password):
            return False
        if not re.search(r'[a-z]', password):
            return False
        if not re.search(r'\d', password):
            return False
        return True

    def validate_email(self, email: str) -> bool:
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def validate_phone(self, phone: str) -> bool:
        """Validate French phone number format"""
        pattern = r'^(?:(?:\+33|0)[1-9](?:[.\-\s]?\d{2}){4})$'
        return re.match(pattern, phone) is not None

    async def register_user(self, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Register a new user with Supabase Auth"""
        try:
            # Validate input data
            if not self.validate_email(user_data.get('email', '')):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid email format"
                )

            if not self.validate_password(user_data.get('password', '')):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Password must be at least 8 characters with uppercase, lowercase, and number"
                )

            phone = user_data.get('phone', '')
            if phone and not self.validate_phone(phone):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid phone number format. Use French format (e.g., 01.23.45.67.89)"
                )

            # Create user with Supabase Auth
            supabase = self.client.get_client()
            
            auth_response = supabase.auth.sign_up({
                "email": user_data['email'],
                "password": user_data['password'],
                "options": {
                    "data": {
                        "first_name": user_data.get('first_name', ''),
                        "last_name": user_data.get('last_name', ''),
                        "company": user_data.get('company', ''),
                        "phone": phone
                    }
                }
            })

            if auth_response.user:
                # Create user profile
                profile_data = {
                    "first_name": user_data.get('first_name', ''),
                    "last_name": user_data.get('last_name', ''),
                    "company": user_data.get('company', ''),
                    "phone": phone,
                    "role": "user",
                    "is_active": True
                }

                await self.client.create_user_profile(auth_response.user.id, profile_data)

                return {
                    "message": "User registered successfully",
                    "user_id": auth_response.user.id,
                    "email": auth_response.user.email,
                    "email_confirmed": auth_response.user.email_confirmed_at is not None
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Failed to create user account"
                )

        except HTTPException:
            raise
        except Exception as e:
            error_message = str(e)
            if "User already registered" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )
            elif "Invalid email" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid email address"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Registration failed: {error_message}"
                )

    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Login user with Supabase Auth"""
        try:
            supabase = self.client.get_client()
            
            auth_response = supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if auth_response.user and auth_response.session:
                # Get user profile
                profile = await self.client.get_user_profile(auth_response.user.id)
                
                return {
                    "access_token": auth_response.session.access_token,
                    "refresh_token": auth_response.session.refresh_token,
                    "token_type": "bearer",
                    "expires_in": auth_response.session.expires_in,
                    "user": {
                        "id": auth_response.user.id,
                        "email": auth_response.user.email,
                        "first_name": profile.get('first_name', '') if profile else '',
                        "last_name": profile.get('last_name', '') if profile else '',
                        "company": profile.get('company', '') if profile else '',
                        "phone": profile.get('phone', '') if profile else '',
                        "role": profile.get('role', 'user') if profile else 'user',
                        "is_active": profile.get('is_active', True) if profile else True
                    }
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )

        except HTTPException:
            raise
        except Exception as e:
            error_message = str(e)
            if "Invalid login credentials" in error_message:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid email or password"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Login failed: {error_message}"
                )

    async def verify_token(self, token: str) -> Optional[Dict[str, Any]]:
        """Verify JWT token and return user data"""
        try:
            user_data = await self.client.verify_jwt_token(token)
            
            if user_data:
                # Get user profile
                profile = await self.client.get_user_profile(user_data['id'])
                
                return {
                    "id": user_data['id'],
                    "email": user_data['email'],
                    "first_name": profile.get('first_name', '') if profile else '',
                    "last_name": profile.get('last_name', '') if profile else '',
                    "company": profile.get('company', '') if profile else '',
                    "phone": profile.get('phone', '') if profile else '',
                    "role": profile.get('role', 'user') if profile else 'user',
                    "is_active": profile.get('is_active', True) if profile else True
                }
            
            return None

        except Exception as e:
            print(f"Token verification error: {str(e)}")
            return None

    async def refresh_token(self, refresh_token: str) -> Dict[str, Any]:
        """Refresh access token"""
        try:
            supabase = self.client.get_client()
            
            auth_response = supabase.auth.refresh_session(refresh_token)

            if auth_response.session:
                return {
                    "access_token": auth_response.session.access_token,
                    "refresh_token": auth_response.session.refresh_token,
                    "token_type": "bearer",
                    "expires_in": auth_response.session.expires_in
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid refresh token"
                )

        except HTTPException:
            raise
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Token refresh failed: {str(e)}"
            )

    async def logout_user(self, token: str) -> Dict[str, Any]:
        """Logout user"""
        try:
            supabase = self.client.get_client()
            supabase.auth.sign_out()
            
            return {"message": "Logged out successfully"}

        except Exception as e:
            # Even if logout fails, we consider it successful from client perspective
            return {"message": "Logged out successfully"}

# Create global instance
supabase_auth = SupabaseAuth()