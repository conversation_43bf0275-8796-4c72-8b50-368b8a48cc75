#!/usr/bin/env python3
"""
Script pour recréer l'utilisateur admin en gérant les contraintes
"""

import asyncio
import asyncpg
from datetime import datetime
from app.core.config import settings
from app.services.supabase_service import supabase_service

DATABASE_URL = settings.DATABASE_URL


async def recreate_admin_safe():
    """Recréer l'utilisateur admin en gérant les contraintes de clés étrangères"""
    print("🔄 Recréation sécurisée de l'utilisateur admin...")
    
    admin_email = "<EMAIL>"
    admin_password = "Admin123!"  # Nouveau mot de passe plus sûr
    
    try:
        # 1. Mettre à jour le mot de passe Supabase sans supprimer l'utilisateur
        print("   🔑 Mise à jour du mot de passe Supabase...")
        
        users_data = await supabase_service.list_users()
        current_user_id = None
        
        for user in users_data.get("users", []):
            if user.get("email") == admin_email:
                current_user_id = user.get("id")
                break
        
        if current_user_id:
            # Mettre à jour le mot de passe et les métadonnées
            await supabase_service.update_user(
                current_user_id,
                password=admin_password,
                user_metadata={
                    "first_name": "Admin",
                    "last_name": "ORBIS",
                    "role": "admin",
                    "company": "ORBIS"
                }
            )
            print(f"   ✅ Mot de passe Supabase mis à jour pour: {current_user_id[:8]}...")
        else:
            # Créer un nouvel utilisateur si aucun n'existe
            print("   👤 Création d'un nouvel utilisateur Supabase...")
            
            user_metadata = {
                "first_name": "Admin",
                "last_name": "ORBIS",
                "role": "admin",
                "company": "ORBIS"
            }
            
            new_user = await supabase_service.create_user(
                email=admin_email,
                password=admin_password,
                user_metadata=user_metadata
            )
            
            # Extraire l'ID utilisateur
            if "user" in new_user:
                current_user_id = new_user["user"]["id"]
            elif "id" in new_user:
                current_user_id = new_user["id"]
            else:
                # Récupérer depuis la liste
                users_data = await supabase_service.list_users()
                for user in users_data.get("users", []):
                    if user.get("email") == admin_email:
                        current_user_id = user.get("id")
                        break
            
            print(f"   ✅ Nouvel utilisateur Supabase créé: {current_user_id[:8]}...")
        
        # 2. Mettre à jour l'utilisateur local sans le supprimer
        print("   🏠 Mise à jour de l'utilisateur local...")
        
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Vérifier si l'utilisateur local existe
            local_user = await conn.fetchrow("""
                SELECT id, supabase_user_id FROM users WHERE email = $1
            """, admin_email)
            
            if local_user:
                # Mettre à jour l'utilisateur existant
                await conn.execute("""
                    UPDATE users 
                    SET supabase_user_id = $1, first_name = $2, last_name = $3, 
                        role = $4, is_active = $5, is_verified = $6, updated_at = $7
                    WHERE email = $8
                """,
                current_user_id, "Admin", "ORBIS", "ADMIN", True, True, 
                datetime.utcnow(), admin_email
                )
                print(f"   ✅ Utilisateur local mis à jour (ID: {local_user['id']})")
                
            else:
                # Créer un nouvel utilisateur local
                company = await conn.fetchrow("SELECT id FROM companies WHERE name = 'ORBIS'")
                if not company:
                    print("   ❌ Entreprise ORBIS non trouvée")
                    return None, None
                
                user_id = await conn.fetchval("""
                    INSERT INTO users (
                        supabase_user_id, email, first_name, last_name, 
                        role, is_active, is_superuser, is_verified, 
                        created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                    RETURNING id
                """,
                current_user_id, admin_email, "Admin", "ORBIS", "ADMIN",
                True, True, True, datetime.utcnow(), datetime.utcnow()
                )
                
                # Associer à l'entreprise
                await conn.execute("""
                    INSERT INTO user_companies (
                        user_id, company_id, role, is_default, is_active,
                        joined_at, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                user_id, company['id'], 'admin', True, True,
                datetime.utcnow(), datetime.utcnow(), datetime.utcnow()
                )
                
                print(f"   ✅ Nouvel utilisateur local créé (ID: {user_id})")
            
            # 3. Synchroniser user_profiles
            print("   👥 Synchronisation du profil...")
            
            # Vérifier si le profil existe
            profile_exists = await conn.fetchval("""
                SELECT EXISTS(SELECT 1 FROM user_profiles WHERE id = $1)
            """, current_user_id)
            
            if profile_exists:
                # Mettre à jour le profil existant
                await conn.execute("""
                    UPDATE user_profiles 
                    SET first_name = $1, last_name = $2, company = $3, 
                        role = $4, is_active = $5, updated_at = $6
                    WHERE id = $7
                """,
                "Admin", "ORBIS", "ORBIS", "admin", True, datetime.utcnow(), current_user_id
                )
                print(f"   ✅ Profil mis à jour")
            else:
                # Créer un nouveau profil
                await conn.execute("""
                    INSERT INTO user_profiles (
                        id, first_name, last_name, company, role, 
                        is_active, created_at, updated_at
                    )
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                """,
                current_user_id, "Admin", "ORBIS", "ORBIS", "admin",
                True, datetime.utcnow(), datetime.utcnow()
                )
                print(f"   ✅ Nouveau profil créé")
        
        finally:
            await conn.close()
        
        return current_user_id, admin_password
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return None, None


async def verify_admin_setup(user_id, password):
    """Vérifier la configuration de l'admin"""
    print("\n🔍 Vérification de la configuration admin...")
    
    try:
        conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
        
        try:
            # Vérifier l'utilisateur complet
            admin_info = await conn.fetchrow("""
                SELECT 
                    u.id, u.email, u.first_name, u.last_name, u.role, u.supabase_user_id,
                    u.is_active, u.is_verified,
                    c.name as company_name, uc.role as company_role,
                    up.first_name as profile_first_name, up.last_name as profile_last_name
                FROM users u
                LEFT JOIN user_companies uc ON u.id = uc.user_id
                LEFT JOIN companies c ON uc.company_id = c.id
                LEFT JOIN user_profiles up ON u.supabase_user_id = up.id::text
                WHERE u.email = '<EMAIL>'
            """)
            
            if admin_info:
                print("   ✅ Configuration admin complète:")
                print(f"     📧 Email: {admin_info['email']}")
                print(f"     👤 Nom: {admin_info['first_name']} {admin_info['last_name']}")
                print(f"     🏢 Entreprise: {admin_info['company_name']} ({admin_info['company_role']})")
                print(f"     🔑 Rôle: {admin_info['role']}")
                print(f"     🆔 Supabase ID: {admin_info['supabase_user_id'][:8]}...")
                print(f"     ✅ Actif: {admin_info['is_active']}")
                print(f"     ✅ Vérifié: {admin_info['is_verified']}")
                print(f"     👥 Profil: {admin_info['profile_first_name']} {admin_info['profile_last_name']}")
                
                return True
            else:
                print("   ❌ Configuration admin incomplète")
                return False
        
        finally:
            await conn.close()
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Recréation Sécurisée de l'Utilisateur Admin")
    print("="*60)
    
    result = await recreate_admin_safe()
    
    if result[0]:  # user_id
        user_id, password = result
        
        if await verify_admin_setup(user_id, password):
            print("\n✅ Utilisateur admin configuré avec succès!")
            
            print("\n🔑 IDENTIFIANTS DE CONNEXION:")
            print("="*50)
            print(f"📧 Email: <EMAIL>")
            print(f"🔑 Mot de passe: {password}")
            print("="*50)
            
            print("\n🎯 Instructions de test:")
            print("   1. Aller sur: http://localhost:3001/login")
            print("   2. Utiliser les identifiants ci-dessus")
            print("   3. Le mot de passe a été mis à jour dans Supabase")
            
            print("\n📝 Informations:")
            print("   • Mot de passe stocké dans: Supabase Auth")
            print("   • Données utilisateur dans: table 'users'")
            print("   • Profil dans: table 'user_profiles'")
            print("   • Toutes les contraintes respectées")
            
            return True
    
    print("\n❌ Échec de la configuration de l'utilisateur admin")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
