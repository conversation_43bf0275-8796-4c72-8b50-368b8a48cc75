#!/usr/bin/env python3
import requests
import json
from datetime import datetime

print('🚀 CRÉATION DES TABLES SUPABASE AVEC SERVICE ROLE KEY')
print('=' * 70)

# Configuration avec la VRAIE service role key
SUPABASE_URL = 'https://ckqxfylgfcbutcwvqepp.supabase.co'
SERVICE_KEY = 'REMPLACER_PAR_LA_VRAIE_SERVICE_ROLE_KEY'

headers = {
    'apikey': SERVICE_KEY,
    'Authorization': f'Bearer {SERVICE_KEY}',
    'Content-Type': 'application/json'
}

# Test de connexion
print('🔗 Test de connexion avec service role key...')
try:
    response = requests.get(f'{SUPABASE_URL}/rest/v1/', headers=headers)
    if response.status_code == 200:
        print('✅ Connexion réussie!')
        connection_ok = True
    else:
        print(f'❌ Échec connexion: {response.status_code}')
        print(f'Response: {response.text}')
        connection_ok = False
except Exception as e:
    print(f'❌ Erreur: {e}')
    connection_ok = False

if not connection_ok:
    exit(1)

# Script SQL pour créer toutes les tables
sql_create_tables = """
-- Table companies
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    siret VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table user_profiles
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID REFERENCES public.companies(id),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(50),
    role VARCHAR(50) DEFAULT 'employee',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table projects
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    company_id UUID REFERENCES public.companies(id),
    client_name VARCHAR(255),
    client_address TEXT,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'planning',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table employees
CREATE TABLE IF NOT EXISTS public.employees (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES public.companies(id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    position VARCHAR(100),
    hourly_rate DECIMAL(10,2),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table time_entries
CREATE TABLE IF NOT EXISTS public.time_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    employee_id UUID REFERENCES public.employees(id),
    date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    hours_worked DECIMAL(5,2),
    description TEXT,
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table documents
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_type VARCHAR(50),
    file_size INTEGER,
    description TEXT,
    uploaded_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table tasks
CREATE TABLE IF NOT EXISTS public.tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    assigned_to UUID REFERENCES public.employees(id),
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(20) DEFAULT 'medium',
    due_date DATE,
    is_completed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table materials
CREATE TABLE IF NOT EXISTS public.materials (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    quantity DECIMAL(10,2),
    unit VARCHAR(50),
    unit_price DECIMAL(10,2),
    total_price DECIMAL(12,2),
    supplier VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table invoices
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES public.projects(id),
    invoice_number VARCHAR(100) UNIQUE,
    amount DECIMAL(15,2),
    tax_amount DECIMAL(15,2),
    total_amount DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'draft',
    issue_date DATE,
    due_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table clients
CREATE TABLE IF NOT EXISTS public.clients (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES public.companies(id),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    contact_person VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table suppliers
CREATE TABLE IF NOT EXISTS public.suppliers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    company_id UUID REFERENCES public.companies(id),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    contact_person VARCHAR(255),
    specialty VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS on all tables
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.materials ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.suppliers ENABLE ROW LEVEL SECURITY;
"""

print('\n📋 Exécution du script SQL pour créer toutes les tables...')
try:
    # Utilisation de l'endpoint SQL direct
    sql_url = f'{SUPABASE_URL}/rest/v1/rpc/exec'
    response = requests.post(sql_url, headers=headers, json={'sql': sql_create_tables})
    
    if response.status_code in [200, 201]:
        print('✅ Script SQL exécuté avec succès!')
    else:
        print(f'⚠️ Réponse SQL: {response.status_code}')
        print(f'Response: {response.text}')
        
        # Essai avec une autre méthode
        print('\n🔄 Tentative avec méthode alternative...')
        # Diviser les commandes SQL
        sql_commands = sql_create_tables.split(';')
        success_count = 0
        
        for i, command in enumerate(sql_commands):
            if command.strip():
                try:
                    response = requests.post(
                        f'{SUPABASE_URL}/rest/v1/rpc/exec',
                        headers=headers,
                        json={'sql': command.strip() + ';'}
                    )
                    if response.status_code in [200, 201]:
                        success_count += 1
                        print(f'  ✅ Commande {i+1} réussie')
                    else:
                        print(f'  ⚠️ Commande {i+1}: {response.status_code}')
                except Exception as e:
                    print(f'  ❌ Erreur commande {i+1}: {e}')
        
        print(f'📊 {success_count}/{len([c for c in sql_commands if c.strip()])} commandes réussies')
        
except Exception as e:
    print(f'❌ Erreur script SQL: {e}')

# Vérification des tables créées
print('\n🔍 Vérification des tables créées...')
tables_to_check = [
    'companies', 'user_profiles', 'projects', 'employees', 'time_entries',
    'documents', 'tasks', 'materials', 'invoices', 'clients', 'suppliers'
]

tables_created = []
for table in tables_to_check:
    try:
        check_url = f'{SUPABASE_URL}/rest/v1/{table}?limit=1'
        response = requests.get(check_url, headers=headers)
        if response.status_code == 200:
            tables_created.append(table)
            print(f'  ✅ {table}: Accessible')
        else:
            print(f'  ❌ {table}: Status {response.status_code}')
    except Exception as e:
        print(f'  ❌ {table}: Erreur {e}')

# Test d'insertion de données d'exemple
if 'companies' in tables_created:
    print('\n🧪 Test d\'insertion de données d\'exemple...')
    test_company = {
        'name': 'ORBIS Test Construction',
        'address': '123 Rue de la Construction, 75001 Paris',
        'phone': '+33123456789',
        'email': '<EMAIL>',
        'siret': '12345678901234'
    }
    
    try:
        insert_url = f'{SUPABASE_URL}/rest/v1/companies'
        response = requests.post(insert_url, headers=headers, json=test_company)
        if response.status_code in [200, 201]:
            result = response.json()
            print('✅ Données de test insérées!')
            if isinstance(result, list) and len(result) > 0:
                print(f'   ID: {result[0].get("id", "N/A")}')
                print(f'   Nom: {result[0].get("name", "N/A")}')
        else:
            print(f'⚠️ Insertion: {response.status_code}')
            print(f'Response: {response.text}')
    except Exception as e:
        print(f'❌ Erreur insertion: {e}')

# Rapport final
print(f'\n📊 RAPPORT FINAL')
print('=' * 50)
print(f'✅ Tables créées: {len(tables_created)}/{len(tables_to_check)}')
for table in tables_created:
    print(f'  • {table}')

if len(tables_created) == len(tables_to_check):
    print('\n🎉 SUCCÈS COMPLET! Toutes les tables ont été créées.')
else:
    print(f'\n⚠️ SUCCÈS PARTIEL: {len(tables_created)} tables sur {len(tables_to_check)}')

print('\n🔚 PROCESSUS TERMINÉ!')
