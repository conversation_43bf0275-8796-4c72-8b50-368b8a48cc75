# app/api/deps.py
from typing import Optional, List
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core import security
from app.core.config import settings
from app.core.database import get_db
from app.models.user import User, UserRole
from app.models.company import Company, UserCompany, CompanyRole
from app.schemas.user import TokenPayload
from app.middleware.tenant_middleware import (
    get_current_company, get_current_company_id,
    get_current_user_role, require_company_access, require_company_role
)

reusable_oauth2 = HTTPBearer()

async def get_current_token(
    token: HTTPAuthorizationCredentials = Depends(reusable_oauth2)
) -> str:
    """Extract the current token string"""
    return token.credentials

async def get_current_user(
    db: AsyncSession = Depends(get_db), 
    token: HTTPAuthorizationCredentials = Depends(reusable_oauth2)
) -> User:
    """Get current user from JWT token"""
    try:
        # Use the enhanced token verification
        payload = security.verify_token(token.credentials, token_type="access")
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )
        
        token_data = TokenPayload(**payload)
        
    except (jwt.JWTError, ValidationError, Exception):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    
    if not token_data.sub:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid token payload"
        )
    
    result = await db.execute(select(User).where(User.id == token_data.sub))
    user = result.scalar_one_or_none()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user

async def get_current_active_superuser(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return current_user

def require_permissions(required_permissions: List[str]):
    """Dependency factory for permission-based access control"""
    async def permission_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        user_permissions = security.get_user_permissions(current_user.role)
        
        # Check if user has all required permissions
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return current_user
    
    return permission_checker

def require_role(required_roles: List[UserRole]):
    """Dependency factory for role-based access control"""
    async def role_checker(
        current_user: User = Depends(get_current_active_user)
    ) -> User:
        if current_user.role not in required_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied. Required roles: {[role.value for role in required_roles]}"
            )
        
        return current_user
    
    return role_checker

# Common permission dependencies
get_admin_user = require_role([UserRole.SUPER_ADMIN, UserRole.ADMIN])
get_manager_user = require_role([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.CHEF_PROJET])
get_user_manager = require_permissions(['manage_users'])
get_project_manager = require_permissions(['manage_projects'])

async def get_company_access(
    company_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Company:
    """Verify user has access to the specified company"""
    result = await db.execute(
        select(UserCompany).where(
            UserCompany.user_id == current_user.id,
            UserCompany.company_id == company_id
        )
    )
    user_company = result.scalar_one_or_none()
    
    if not user_company:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to this company"
        )
    
    result = await db.execute(select(Company).where(Company.id == company_id))
    company = result.scalar_one_or_none()
    if not company or not company.is_active:
        raise HTTPException(status_code=404, detail="Company not found")
    
    return company


# New tenant-aware dependencies
def get_current_tenant_company(request: Request) -> Company:
    """Get current company from tenant middleware"""
    return require_company_access(request)


def require_admin_role(request: Request) -> str:
    """Require admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN])


def require_manager_role(request: Request) -> str:
    """Require manager or admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN, CompanyRole.MANAGER])


def require_user_role(request: Request) -> str:
    """Require user, manager or admin role in current company"""
    return require_company_role(request, [CompanyRole.ADMIN, CompanyRole.MANAGER, CompanyRole.USER])


async def get_user_companies(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> List[UserCompany]:
    """Get all companies for current user"""
    result = await db.execute(
        select(UserCompany, Company)
        .join(Company, UserCompany.company_id == Company.id)
        .where(
            UserCompany.user_id == current_user.id,
            UserCompany.is_active == True,
            Company.is_active == True
        )
        .order_by(UserCompany.is_default.desc(), Company.name.asc())
    )
    return [uc for uc, _ in result.all()]