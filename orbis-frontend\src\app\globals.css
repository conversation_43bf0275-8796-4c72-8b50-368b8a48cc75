@tailwind base;
@tailwind components;
@tailwind utilities;

/* Design System Variables */
:root {
  /* Primary Colors */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-200: #bae6fd;
  --primary-300: #7dd3fc;
  --primary-400: #38bdf8;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  --primary-800: #075985;
  --primary-900: #0c4a6e;

  /* Gradient Colors */
  --gradient-blue: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-purple: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --gradient-orange: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --gradient-green: linear-gradient(135deg, #d299c2 0%, #fef9d3 100%);

  /* Modern Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* Modern Card Styles */
.modern-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300;
}

.gradient-card-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  @apply text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
}

.gradient-card-purple {
  background: linear-gradient(135deg, #a855f7 0%, #ec4899 100%);
  @apply text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
}

.gradient-card-orange {
  background: linear-gradient(135deg, #f59e0b 0%, #ef4444 100%);
  @apply text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
}

.gradient-card-green {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  @apply text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300;
}

/* Modern Button Styles */
.btn-modern {
  @apply px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-delay-100 {
  animation-delay: 0.1s;
}

.animate-delay-200 {
  animation-delay: 0.2s;
}

.animate-delay-300 {
  animation-delay: 0.3s;
}

/* Modern Sidebar */
.sidebar-modern {
  @apply bg-white border-r border-gray-200 shadow-lg;
}

.sidebar-item {
  @apply flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200 rounded-lg mx-2;
}

.sidebar-item.active {
  @apply bg-blue-100 text-blue-700 font-medium;
}

/* Modern Typography */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

:root {
  --primary-50: 240, 249, 255;
  --primary-100: 224, 242, 254;
  --primary-200: 186, 230, 253;
  --primary-300: 125, 211, 252;
  --primary-400: 56, 189, 248;
  --primary-500: 14, 165, 233;
  --primary-600: 2, 132, 199;
  --primary-700: 3, 105, 161;
  --primary-800: 7, 89, 133;
  --primary-900: 12, 74, 110;
  --primary-950: 8, 47, 73;
}

/* Base Styles */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply text-gray-800 bg-gray-50;
  }

  h1 {
    @apply text-3xl font-bold text-gray-900 mb-6;
  }

  h2 {
    @apply text-2xl font-bold text-gray-800 mb-4;
  }

  h3 {
    @apply text-xl font-semibold text-gray-800 mb-3;
  }

  h4 {
    @apply text-lg font-semibold text-gray-800 mb-2;
  }
}

/* Component Styles */
@layer components {
  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-card hover:shadow-card-hover transition-shadow duration-300 overflow-hidden;
  }

  .card-header {
    @apply p-4 border-b border-gray-100 flex items-center justify-between;
  }

  .card-body {
    @apply p-4;
  }

  .card-footer {
    @apply p-4 bg-gray-50 border-t border-gray-100;
  }

  .form-input {
    @apply rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-25;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .badge {
    @apply inline-flex items-center px-2 py-0.5 rounded text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }
}

/* Custom scrollbar */
.scrollbar-thin::-webkit-scrollbar {
  width: 5px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Animation Delay Utilities */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: #fff;
    color: #000;
  }
  
  .print-break-inside-avoid {
    break-inside: avoid;
  }
}