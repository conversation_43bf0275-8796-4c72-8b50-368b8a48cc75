
-- ORBIS Suivi Travaux Database Schema
-- Execute this script in Supabase SQL Editor

-- 1. Companies table (multi-tenant core)
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    siret VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. User profiles table (linked to Supabase Auth)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name VA<PERSON>HA<PERSON>(100),
    phone VARCHAR(50),
    role VARCHAR(50) DEFAULT 'employee',
    company_id UUID REFERENCES public.companies(id),
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Projects table
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'draft',
    company_id UUID REFERENCES public.companies(id),
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. Employees table
CREATE TABLE IF NOT EXISTS public.employees (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_number VARCHAR(50),
    user_profile_id UUID REFERENCES public.user_profiles(id),
    position VARCHAR(100),
    hourly_rate DECIMAL(10,2),
    hire_date DATE,
    company_id UUID REFERENCES public.companies(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Time entries table
CREATE TABLE IF NOT EXISTS public.time_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID REFERENCES public.employees(id),
    project_id UUID REFERENCES public.projects(id),
    date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    hours_worked DECIMAL(5,2),
    description TEXT,
    company_id UUID REFERENCES public.companies(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. Documents table
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    project_id UUID REFERENCES public.projects(id),
    uploaded_by UUID REFERENCES public.user_profiles(id),
    company_id UUID REFERENCES public.companies(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security (RLS) on all tables
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Create performance indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_company_id ON public.user_profiles(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_company_id ON public.projects(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_employees_company_id ON public.employees(company_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_company_id ON public.time_entries(company_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_date ON public.time_entries(date);
CREATE INDEX IF NOT EXISTS idx_time_entries_employee_id ON public.time_entries(employee_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_project_id ON public.time_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON public.documents(project_id);

-- Insert sample data
INSERT INTO public.companies (name, address, phone, email, siret) VALUES
('ORBIS Construction', '123 Rue de la Paix, Paris, France', '+33 1 23 45 67 89', '<EMAIL>', '12345678901234')
ON CONFLICT DO NOTHING;

-- Get the company ID for sample data
DO $$
DECLARE
    company_uuid UUID;
    user_uuid UUID;
    project_uuid UUID;
    employee_uuid UUID;
BEGIN
    -- Get company ID
    SELECT id INTO company_uuid FROM public.companies WHERE name = 'ORBIS Construction' LIMIT 1;
    
    -- Insert sample user profile
    INSERT INTO public.user_profiles (email, first_name, last_name, role, company_id) VALUES
    ('<EMAIL>', 'Jean', 'Dupont', 'admin', company_uuid)
    ON CONFLICT (email) DO NOTHING;
    
    -- Get user ID
    SELECT id INTO user_uuid FROM public.user_profiles WHERE email = '<EMAIL>' LIMIT 1;
    
    -- Insert sample project
    INSERT INTO public.projects (name, description, address, start_date, budget, status, company_id, created_by) VALUES
    ('Projet Résidentiel Paris 15', 'Construction d''un immeuble résidentiel', '15 Avenue de la République, Paris 15', CURRENT_DATE, 500000.00, 'active', company_uuid, user_uuid)
    ON CONFLICT DO NOTHING;
    
    -- Get project ID
    SELECT id INTO project_uuid FROM public.projects WHERE name = 'Projet Résidentiel Paris 15' LIMIT 1;
    
    -- Insert sample employee
    INSERT INTO public.employees (employee_number, user_profile_id, position, hourly_rate, hire_date, company_id) VALUES
    ('EMP001', user_uuid, 'Chef de chantier', 35.00, CURRENT_DATE, company_uuid)
    ON CONFLICT DO NOTHING;
    
    -- Get employee ID
    SELECT id INTO employee_uuid FROM public.employees WHERE employee_number = 'EMP001' LIMIT 1;
    
    -- Insert sample time entry
    INSERT INTO public.time_entries (employee_id, project_id, date, start_time, end_time, hours_worked, description, company_id) VALUES
    (employee_uuid, project_uuid, CURRENT_DATE, '08:00', '17:00', 8.0, 'Travaux de fondation', company_uuid)
    ON CONFLICT DO NOTHING;
END $$;

-- Create RLS policies for multi-tenant security
CREATE POLICY "Users can view their own company data" ON public.companies
    FOR SELECT USING (id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));

CREATE POLICY "Users can view profiles in their company" ON public.user_profiles
    FOR SELECT USING (company_id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));

CREATE POLICY "Users can view projects in their company" ON public.projects
    FOR SELECT USING (company_id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));

CREATE POLICY "Users can view employees in their company" ON public.employees
    FOR SELECT USING (company_id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));

CREATE POLICY "Users can view time entries in their company" ON public.time_entries
    FOR SELECT USING (company_id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));

CREATE POLICY "Users can view documents in their company" ON public.documents
    FOR SELECT USING (company_id IN (
        SELECT company_id FROM public.user_profiles 
        WHERE id = auth.uid()
    ));
