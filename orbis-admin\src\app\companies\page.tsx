'use client'

import { useState, useEffect } from 'react'
import { Building2, Plus, Search, Edit, Trash2, Users, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

interface Company {
  id: number
  name: string
  code: string
  siret: string
  address: string
  phone: string
  email: string
  is_active: boolean
  created_at: string
  user_count?: number
  project_count?: number
}

export default function CompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [showCreateModal, setShowCreateModal] = useState(false)

  useEffect(() => {
    fetchCompanies()
  }, [])

  const fetchCompanies = async () => {
    try {
      // TODO: Replace with actual API call
      // const response = await fetch('/api/admin/companies')
      // const data = await response.json()
      
      // Mock data for now
      const mockCompanies: Company[] = [
        {
          id: 1,
          name: "<PERSON><PERSON><PERSON>",
          code: "ORBIS001",
          siret: "12345678901234",
          address: "123 Avenue de la Construction, 75001 Paris",
          phone: "***********.89",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-01-15T10:00:00Z",
          user_count: 5,
          project_count: 12
        },
        {
          id: 2,
          name: "Construction Plus",
          code: "CONST001",
          siret: "98765432109876",
          address: "456 Rue du Bâtiment, 69000 Lyon",
          phone: "***********.12",
          email: "<EMAIL>",
          is_active: true,
          created_at: "2024-02-01T14:30:00Z",
          user_count: 8,
          project_count: 25
        }
      ]
      
      setCompanies(mockCompanies)
    } catch (error) {
      console.error('Error fetching companies:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    company.email.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="mr-4">
                <ArrowLeft className="h-6 w-6 text-gray-600 hover:text-gray-900" />
              </Link>
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">Gestion des Entreprises</h1>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Nouvelle Entreprise
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Rechercher une entreprise..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full max-w-md border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Companies Grid */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCompanies.map((company) => (
              <div key={company.id} className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
                    <p className="text-sm text-gray-500">{company.code}</p>
                  </div>
                  <div className="flex space-x-2">
                    <button className="text-gray-400 hover:text-blue-600">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-gray-400 hover:text-red-600">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600">{company.email}</p>
                  <p className="text-sm text-gray-600">{company.phone}</p>
                  <p className="text-sm text-gray-600">{company.address}</p>
                </div>

                <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <Users className="h-4 w-4 mr-1" />
                      {company.user_count} utilisateurs
                    </div>
                    <div className="text-sm text-gray-500">
                      {company.project_count} projets
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    company.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {company.is_active ? 'Actif' : 'Inactif'}
                  </span>
                </div>

                <div className="mt-4">
                  <Link
                    href={`/companies/${company.id}`}
                    className="w-full bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-4 rounded text-center block transition-colors"
                  >
                    Voir les détails
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {filteredCompanies.length === 0 && !loading && (
          <div className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune entreprise trouvée</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Essayez de modifier votre recherche.' : 'Commencez par créer une nouvelle entreprise.'}
            </p>
          </div>
        )}
      </main>

      {/* Create Company Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4">Créer une nouvelle entreprise</h3>
            <p className="text-gray-600 mb-4">
              Cette fonctionnalité sera bientôt disponible. Pour l'instant, les entreprises peuvent être créées via l'API.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Fermer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
