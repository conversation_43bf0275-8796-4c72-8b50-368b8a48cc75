# app/api/api_v1/endpoints/projects.py
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.models.project import Project, ProjectEmployee
from app.models.employee import Employee
from app.schemas.project import Project as ProjectSchema, ProjectCreate, ProjectUpdate

router = APIRouter()

@router.get("/", response_model=List[ProjectSchema])
def read_projects(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    company_id: int = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve projects.
    """
    if company_id:
        deps.get_company_access(company_id, current_user, db)
        projects = db.query(Project).filter(Project.company_id == company_id).offset(skip).limit(limit).all()
    else:
        # Get all projects from user's companies
        from app.models.company import UserCompany
        user_companies = db.query(UserCompany).filter(UserCompany.user_id == current_user.id).all()
        company_ids = [uc.company_id for uc in user_companies]
        projects = db.query(Project).filter(Project.company_id.in_(company_ids)).offset(skip).limit(limit).all()
    
    return projects

@router.post("/", response_model=ProjectSchema)
def create_project(
    *,
    db: Session = Depends(deps.get_db),
    project_in: ProjectCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create new project.
    """
    deps.get_company_access(project_in.company_id, current_user, db)
    
    # Check if code already exists for this company
    existing = db.query(Project).filter(
        Project.company_id == project_in.company_id,
        Project.code == project_in.code
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Project code already exists for this company")
    
    project = Project(**project_in.dict())
    db.add(project)
    db.commit()
    db.refresh(project)
    return project

@router.get("/{id}", response_model=ProjectSchema)
def read_project(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get project by ID.
    """
    project = db.query(Project).filter(Project.id == id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    deps.get_company_access(project.company_id, current_user, db)
    return project

@router.put("/{id}", response_model=ProjectSchema)
def update_project(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    project_in: ProjectUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a project.
    """
    project = db.query(Project).filter(Project.id == id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    deps.get_company_access(project.company_id, current_user, db)
    
    for field, value in project_in.dict(exclude_unset=True).items():
        setattr(project, field, value)
    
    db.add(project)
    db.commit()
    db.refresh(project)
    return project

@router.post("/{id}/employees", response_model=dict)
def assign_employee_to_project(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    employee_id: int,
    role: str = None,
    hourly_rate: float = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Assign employee to project.
    """
    project = db.query(Project).filter(Project.id == id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    deps.get_company_access(project.company_id, current_user, db)
    
    employee = db.query(Employee).filter(
        Employee.id == employee_id,
        Employee.company_id == project.company_id
    ).first()
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    
    # Check if already assigned
    existing = db.query(ProjectEmployee).filter(
        ProjectEmployee.project_id == id,
        ProjectEmployee.employee_id == employee_id,
        ProjectEmployee.is_active == True
    ).first()
    if existing:
        raise HTTPException(status_code=400, detail="Employee already assigned to project")
    
    assignment = ProjectEmployee(
        project_id=id,
        employee_id=employee_id,
        role=role,
        hourly_rate=hourly_rate
    )
    db.add(assignment)
    db.commit()
    
    return {"message": "Employee assigned successfully"}

@router.delete("/{id}/employees/{employee_id}")
def remove_employee_from_project(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    employee_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Remove employee from project.
    """
    project = db.query(Project).filter(Project.id == id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    deps.get_company_access(project.company_id, current_user, db)
    
    assignment = db.query(ProjectEmployee).filter(
        ProjectEmployee.project_id == id,
        ProjectEmployee.employee_id == employee_id
    ).first()
    if not assignment:
        raise HTTPException(status_code=404, detail="Assignment not found")
    
    assignment.is_active = False
    db.add(assignment)
    db.commit()
    
    return {"message": "Employee removed from project"}