'use client'

import { useAuth } from '@/contexts/AuthContext'
import { usePathname } from 'next/navigation'
import { Navbar } from './Navbar'
import { Sidebar } from './Sidebar'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

// Pages qui ne nécessitent pas d'authentification
const PUBLIC_ROUTES = ['/auth/login', '/auth/register', '/']

export function AppLayout({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuth()
  const pathname = usePathname()

  // Vérifier si la route actuelle est publique
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname) || pathname.startsWith('/auth')

  // Afficher le spinner de chargement pendant la vérification de l'auth
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Si l'utilisateur n'est pas authentifié et tente d'accéder à une route protégée
  if (!isAuthenticated && !isPublicRoute) {
    // Rediriger vers la page de login
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Si l'utilisateur est authentifié et sur une page d'auth, rediriger vers le dashboard
  if (isAuthenticated && isPublicRoute && pathname !== '/') {
    if (typeof window !== 'undefined') {
      window.location.href = '/dashboard'
    }
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Layout pour les pages publiques (auth)
  if (isPublicRoute) {
    return (
      <div className="min-h-screen bg-gray-50">
        {children}
      </div>
    )
  }

  // Layout pour les pages protégées (dashboard, etc.)
  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <div className="flex pt-16">
        <Sidebar />
        <main className="flex-1 transition-all duration-300 lg:ml-64 sm:ml-16 ml-0 p-4 sm:p-6 md:p-8">
          <div className="max-w-7xl mx-auto animate-fade-in">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
