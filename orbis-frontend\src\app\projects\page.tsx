'use client'

import { useState, useEffect } from 'react'
import { ProjectCard } from '@/components/projects/ProjectCard'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card } from '@/components/ui/Card'
import { DashboardWrapper } from '@/components/DashboardWrapper'
import Link from 'next/link'
import { api } from '@/lib/api'

export default function Projects() {
  const [projects, setProjects] = useState<any[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true)
        
        // Vérifier l'authentification
        const token = localStorage.getItem('auth_token')
        if (!token) {
          setError('Vous devez être connecté pour voir les projets')
          return
        }
        
        // Mettre à jour le token dans l'API
        api.setToken(token)
        
        const data = await api.getProjects()
        setProjects(data || [])
        setError(null)
      } catch (err: any) {
        console.error('Error fetching projects:', err)
        
        // Gestion spécifique des erreurs
        if (err.message?.includes('Session expirée')) {
          setError('Session expirée. Redirection vers la connexion...')
          setTimeout(() => {
            window.location.href = '/auth/login'
          }, 2000)
        } else if (err.message?.includes('Ressource non trouvée')) {
          setError('Service des projets temporairement indisponible')
          // Utiliser des données de démonstration
          setProjects([
            {
              id: 1,
              name: "Rénovation Bureau Central",
              code: "RBC-2024-001",
              status: "En cours",
              client_name: "Entreprise ABC",
              start_date: "2024-01-15",
              end_date: "2024-12-15",
              budget_total: 150000,
              description: "Rénovation complète des bureaux centraux",
              progress: 75
            },
            {
              id: 2,
              name: "Construction Entrepôt",
              code: "CE-2024-002", 
              status: "En cours",
              client_name: "Logistique XYZ",
              start_date: "2024-02-01",
              end_date: "2025-01-20",
              budget_total: 300000,
              description: "Construction d'un nouvel entrepôt logistique",
              progress: 45
            },
            {
              id: 3,
              name: "Aménagement Showroom",
              code: "AS-2024-003",
              status: "Terminé",
              client_name: "Retail Pro",
              start_date: "2024-01-01",
              end_date: "2024-06-30",
              budget_total: 80000,
              description: "Aménagement d'un showroom moderne",
              progress: 100
            }
          ])
        } else {
          setError('Erreur lors du chargement des projets')
        }
      } finally {
        setLoading(false)
      }
    }

    fetchProjects()
  }, [])

  const filteredProjects = projects.filter((project: any) => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === 'all' || project.status === filterStatus
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <DashboardWrapper>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <div className="text-lg text-gray-600">Chargement des projets...</div>
          </div>
        </div>
      </DashboardWrapper>
    )
  }

  if (error && projects.length === 0) {
    return (
      <DashboardWrapper>
        <div className="flex justify-center items-center min-h-96">
          <div className="text-center max-w-md">
            <div className="bg-red-50 border border-red-200 rounded-2xl p-8">
              <div className="text-4xl mb-4">⚠️</div>
              <div className="text-red-800 text-lg font-semibold mb-2">Erreur de chargement</div>
              <div className="text-red-600 mb-6">{error}</div>
              <Button 
                onClick={() => window.location.reload()}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                Réessayer
              </Button>
            </div>
          </div>
        </div>
      </DashboardWrapper>
    )
  }

  return (
    <DashboardWrapper>
      <div className="space-y-6">
        {/* Message d'information si données de démonstration */}
        {error && projects.length > 0 && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4">
            <div className="flex items-center">
              <div className="text-yellow-600 mr-3">ℹ️</div>
              <div>
                <div className="text-yellow-800 font-medium">Mode démonstration</div>
                <div className="text-yellow-700 text-sm">Affichage de données d'exemple en attendant la connexion au serveur.</div>
              </div>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Gestion des Projets</h1>
            <p className="text-gray-600 mt-2">Gérez et suivez vos projets de construction</p>
          </div>
          <Link href="/projects/new">
            <Button className="flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
              Nouveau Projet
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <Input
              placeholder="Rechercher un projet..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">Tous les statuts</option>
            <option value="En cours">En cours</option>
            <option value="En attente">En attente</option>
            <option value="Terminé">Terminé</option>
          </select>
        </div>

        {/* Projects Grid */}
        {filteredProjects.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProjects.map((project: any) => (
              <ProjectCard key={project.id} project={project} />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500 text-lg">Aucun projet trouvé</div>
            <p className="text-gray-400 mt-2">
              {searchTerm || filterStatus !== 'all' 
                ? 'Essayez de modifier vos critères de recherche'
                : 'Commencez par créer votre premier projet'
              }
            </p>
          </div>
        )}

        {/* Stats */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Statistiques</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {projects.length}
              </div>
              <div className="text-sm text-gray-600">Total</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {projects.filter((p: any) => p.status === 'En cours').length}
              </div>
              <div className="text-sm text-gray-600">En Cours</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-yellow-600">
                {projects.filter((p: any) => p.status === 'En attente').length}
              </div>
              <div className="text-sm text-gray-600">En Attente</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-600">
                {projects.filter((p: any) => p.status === 'Terminé').length}
              </div>
              <div className="text-sm text-gray-600">Terminés</div>
            </div>
          </div>
        </Card>
      </div>
    </DashboardWrapper>
  )
}
