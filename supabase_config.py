"""
Configuration centralisée pour Supabase - ORBIS Project
========================================================

Ce fichier contient TOUTES les informations de connexion Supabase.
Utilisez ce fichier comme référence unique pour toutes les configurations.

⚠️  IMPORTANT: Mettez à jour la SERVICE_ROLE_KEY avec la vraie clé !
"""

import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# ============================================================================
# CONFIGURATION SUPABASE CENTRALISÉE
# ============================================================================

# Projet Supabase actuel
SUPABASE_PROJECT_ID = "ckqxfylgfcbutcwvqepp"

# URLs Supabase
SUPABASE_URL = "https://ckqxfylgfcbutcwvqepp.supabase.co"
SUPABASE_API_URL = f"{SUPABASE_URL}/rest/v1"
SUPABASE_AUTH_URL = f"{SUPABASE_URL}/auth/v1"

# Clés d'API
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNrcXhmeWxnZmNidXRjd3ZxZXBwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTEwMjEzOTcsImV4cCI6MjA2NjU5NzM5N30.ic9RY0xRUFds3BYdIvpL0YcH5jZIgWDd96QihS8C_t4N"

# ⚠️ REMPLACER PAR LA VRAIE SERVICE_ROLE_KEY
SUPABASE_SERVICE_ROLE_KEY = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "REMPLACER_PAR_LA_VRAIE_SERVICE_ROLE_KEY")

# Base de données PostgreSQL
DATABASE_URL = "**************************************************************************************************/postgres"

# Configuration de connexion directe PostgreSQL
DB_CONFIG = {
    'host': 'aws-0-eu-north-1.pooler.supabase.com',
    'port': 6543,
    'database': 'postgres',
    'user': 'postgres.ckqxfylgfcbutcwvqepp',
    'password': 'E5FACUUHRbaX&'
}

# ============================================================================
# HEADERS POUR LES REQUÊTES API
# ============================================================================

def get_anon_headers():
    """Headers pour les requêtes avec clé anonyme"""
    return {
        'apikey': SUPABASE_ANON_KEY,
        'Authorization': f'Bearer {SUPABASE_ANON_KEY}',
        'Content-Type': 'application/json'
    }

def get_service_headers():
    """Headers pour les requêtes avec service role key"""
    return {
        'apikey': SUPABASE_SERVICE_ROLE_KEY,
        'Authorization': f'Bearer {SUPABASE_SERVICE_ROLE_KEY}',
        'Content-Type': 'application/json'
    }

# ============================================================================
# CONFIGURATION POUR DIFFÉRENTS ENVIRONNEMENTS
# ============================================================================

# Configuration pour le frontend (Next.js)
FRONTEND_CONFIG = {
    'NEXT_PUBLIC_SUPABASE_URL': SUPABASE_URL,
    'NEXT_PUBLIC_SUPABASE_ANON_KEY': SUPABASE_ANON_KEY
}

# Configuration pour le backend (FastAPI)
BACKEND_CONFIG = {
    'SUPABASE_URL': SUPABASE_URL,
    'SUPABASE_ANON_KEY': SUPABASE_ANON_KEY,
    'SUPABASE_SERVICE_ROLE_KEY': SUPABASE_SERVICE_ROLE_KEY,
    'DATABASE_URL': DATABASE_URL
}

# ============================================================================
# FONCTIONS UTILITAIRES
# ============================================================================

def validate_config():
    """Valide que toutes les configurations sont présentes"""
    missing = []
    
    if SUPABASE_SERVICE_ROLE_KEY == "REMPLACER_PAR_LA_VRAIE_SERVICE_ROLE_KEY":
        missing.append("SUPABASE_SERVICE_ROLE_KEY")
    
    if not SUPABASE_URL:
        missing.append("SUPABASE_URL")
    
    if not SUPABASE_ANON_KEY:
        missing.append("SUPABASE_ANON_KEY")
    
    if missing:
        raise ValueError(f"Configuration manquante: {', '.join(missing)}")
    
    return True

def print_config_summary():
    """Affiche un résumé de la configuration"""
    print("=" * 60)
    print("🔧 CONFIGURATION SUPABASE - ORBIS")
    print("=" * 60)
    print(f"📍 Projet ID: {SUPABASE_PROJECT_ID}")
    print(f"🌐 URL: {SUPABASE_URL}")
    print(f"🔑 Anon Key: {SUPABASE_ANON_KEY[:20]}...")
    print(f"🔐 Service Key: {'✅ Configurée' if SUPABASE_SERVICE_ROLE_KEY != 'REMPLACER_PAR_LA_VRAIE_SERVICE_ROLE_KEY' else '❌ À configurer'}")
    print(f"🗄️  Database: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print("=" * 60)

if __name__ == "__main__":
    print_config_summary()
    try:
        validate_config()
        print("✅ Configuration valide !")
    except ValueError as e:
        print(f"❌ {e}")
