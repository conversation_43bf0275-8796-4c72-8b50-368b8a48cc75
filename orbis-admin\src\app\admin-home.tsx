'use client'

import { Building2, Users, Settings, BarChart3, Shield } from 'lucide-react'
import Link from 'next/link'

export default function AdminHome() {
  const adminModules = [
    {
      title: "Gestion des Entreprises",
      description: "<PERSON><PERSON><PERSON>, modifier et gérer les entreprises clientes",
      icon: Building2,
      href: "/companies",
      color: "bg-blue-500"
    },
    {
      title: "Gestion des Utilisateurs",
      description: "Inviter et gérer les utilisateurs par entreprise",
      icon: Users,
      href: "/users",
      color: "bg-green-500"
    },
    {
      title: "Analytics & Rapports",
      description: "Statistiques d'utilisation et rapports globaux",
      icon: BarChart3,
      href: "/analytics",
      color: "bg-purple-500"
    },
    {
      title: "Sécurité & Audit",
      description: "Logs d'audit et gestion de la sécurité",
      icon: Shield,
      href: "/security",
      color: "bg-red-500"
    },
    {
      title: "Configuration",
      description: "Paramètres globaux de la plateforme",
      icon: Settings,
      href: "/settings",
      color: "bg-gray-500"
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-2xl font-bold text-gray-900">ORBIS Admin Portal</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">v1.0.0</span>
              <Link 
                href={process.env.NEXT_PUBLIC_CUSTOMER_APP_URL || 'http://localhost:3000'}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                → App Client
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Tableau de Bord Administrateur
          </h2>
          <p className="text-gray-600">
            Gérez les entreprises, utilisateurs et la configuration de la plateforme ORBIS
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Building2 className="h-8 w-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Entreprises</p>
                <p className="text-2xl font-semibold text-gray-900">12</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Utilisateurs</p>
                <p className="text-2xl font-semibold text-gray-900">248</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Projets Actifs</p>
                <p className="text-2xl font-semibold text-gray-900">89</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Alertes</p>
                <p className="text-2xl font-semibold text-gray-900">3</p>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Modules */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {adminModules.map((module) => {
            const IconComponent = module.icon
            return (
              <Link
                key={module.href}
                href={module.href}
                className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-200 p-6 group"
              >
                <div className="flex items-center mb-4">
                  <div className={`${module.color} rounded-lg p-3 text-white`}>
                    <IconComponent className="h-6 w-6" />
                  </div>
                  <h3 className="ml-4 text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                    {module.title}
                  </h3>
                </div>
                <p className="text-gray-600 text-sm">
                  {module.description}
                </p>
              </Link>
            )
          })}
        </div>
      </main>
    </div>
  )
}
