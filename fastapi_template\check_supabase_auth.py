#!/usr/bin/env python3
"""
Script pour vérifier l'authentification Supabase
"""

import asyncio
from app.core.config import settings
from app.services.supabase_service import supabase_service


async def check_supabase_auth():
    """Vérifier l'authentification Supabase"""
    print("🔐 Vérification de l'authentification Supabase...")
    
    try:
        # 1. Lister les utilisateurs Supabase
        print("\n👥 Utilisateurs dans Supabase Auth:")
        users_data = await supabase_service.list_users()
        users = users_data.get("users", [])
        
        for user in users:
            user_id = user.get("id", "")[:8] + "..."
            email = user.get("email", "")
            created_at = user.get("created_at", "")[:10]
            email_confirmed = user.get("email_confirmed_at") is not None
            last_sign_in = user.get("last_sign_in_at", "")[:10] if user.get("last_sign_in_at") else "Jamais"
            
            print(f"   • {email}")
            print(f"     ID: {user_id}")
            print(f"     Créé: {created_at}")
            print(f"     Email confirmé: {email_confirmed}")
            print(f"     Dernière connexion: {last_sign_in}")
            print(f"     Métadonnées: {user.get('user_metadata', {})}")
            print()
        
        # 2. Tester la connexion avec les identifiants
        print("🔑 Test de <NAME_EMAIL>...")
        
        admin_email = "<EMAIL>"
        admin_password = "orbis123!"
        
        try:
            # Simuler une connexion (nous ne pouvons pas vraiment nous connecter depuis le backend)
            print(f"   📧 Email: {admin_email}")
            print(f"   🔑 Mot de passe: {admin_password}")
            print("   ⚠️ Note: Le test de connexion réel doit se faire depuis le frontend")
            
            # Vérifier si l'utilisateur existe
            admin_user = None
            for user in users:
                if user.get("email") == admin_email:
                    admin_user = user
                    break
            
            if admin_user:
                print("   ✅ Utilisateur trouvé dans Supabase Auth")
                print(f"   📊 Statut: {'Actif' if admin_user.get('email_confirmed_at') else 'Email non confirmé'}")
            else:
                print("   ❌ Utilisateur non trouvé dans Supabase Auth")
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test: {e}")
        
        # 3. Vérifier la configuration
        print("\n⚙️ Configuration Supabase:")
        print(f"   URL: {settings.SUPABASE_URL}")
        print(f"   Service Role Key: {'Configuré' if settings.SUPABASE_SERVICE_ROLE_KEY else 'Manquant'}")
        print(f"   Anon Key: {'Configuré' if settings.SUPABASE_ANON_KEY else 'Manquant'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False


async def suggest_solutions():
    """Proposer des solutions"""
    print("\n💡 Solutions possibles:")
    
    print("\n🔧 Option 1: Réinitialiser le mot de passe Supabase")
    print("   1. Aller dans le dashboard Supabase")
    print("   2. Section Authentication > Users")
    print("   3. Trouver <EMAIL>")
    print("   4. Réinitialiser le mot de passe")
    
    print("\n🔧 Option 2: Créer un nouvel utilisateur admin")
    print("   1. Supprimer l'utilisateur actuel")
    print("   2. Recréer avec un mot de passe connu")
    
    print("\n🔧 Option 3: Utiliser le mode développement")
    print("   1. Aller sur http://localhost:3001/dev-login")
    print("   2. Contourner l'authentification Supabase")
    
    print("\n🔧 Option 4: Vérifier les logs Supabase")
    print("   1. Dashboard Supabase > Logs")
    print("   2. Vérifier les tentatives de connexion")


async def create_new_admin():
    """Créer un nouvel utilisateur admin avec mot de passe connu"""
    print("\n🆕 Création d'un nouvel utilisateur admin...")
    
    try:
        # Supprimer l'ancien utilisateur s'il existe
        print("   🗑️ Suppression de l'ancien utilisateur...")
        
        users_data = await supabase_service.list_users()
        for user in users_data.get("users", []):
            if user.get("email") == "<EMAIL>":
                user_id = user.get("id")
                await supabase_service.delete_user(user_id)
                print(f"   ✅ Ancien utilisateur supprimé: {user_id}")
                break
        
        # Créer le nouvel utilisateur
        print("   👤 Création du nouvel utilisateur...")
        
        user_metadata = {
            "first_name": "Admin",
            "last_name": "ORBIS",
            "role": "admin",
            "company": "ORBIS"
        }
        
        new_user = await supabase_service.create_user(
            email="<EMAIL>",
            password="orbis123!",
            user_metadata=user_metadata
        )
        
        print("   ✅ Nouvel utilisateur créé avec succès!")
        print(f"   📧 Email: <EMAIL>")
        print(f"   🔑 Mot de passe: orbis123!")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Vérification de l'Authentification Supabase")
    print("="*60)
    
    if await check_supabase_auth():
        await suggest_solutions()
        
        print("\n❓ Voulez-vous recréer l'utilisateur admin ? (y/N): ", end="")
        # Note: En mode script, nous ne pouvons pas lire l'input
        # Cette partie serait interactive en mode terminal
        
        print("\n📝 Résumé:")
        print("   • Le mot de passe est stocké dans Supabase Auth")
        print("   • Nos tables locales ne contiennent pas de mots de passe")
        print("   • L'authentification se fait via Supabase")
        
        return True
    else:
        print("\n❌ Échec de la vérification")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
