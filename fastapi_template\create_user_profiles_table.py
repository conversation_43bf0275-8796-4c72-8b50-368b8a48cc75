#!/usr/bin/env python3
"""
Script pour créer la table user_profiles nécessaire pour Supabase Auth
"""

import asyncio
import asyncpg
from datetime import datetime

# Configuration
DATABASE_URL = "**************************************************************************************************/postgres"


async def create_user_profiles_table():
    """Créer la table user_profiles pour Supabase Auth"""
    print("🔧 Création de la table user_profiles...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Créer la table user_profiles
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS user_profiles (
                id UUID PRIMARY KEY,
                first_name VARCHAR(100),
                last_name VA<PERSON><PERSON><PERSON>(100),
                company VARCHAR(200),
                phone VARCHAR(20),
                role VARCHAR(50) DEFAULT 'user',
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
        """)
        
        print("   ✅ Table user_profiles créée")
        
        # Créer un index sur l'ID pour les performances
        await conn.execute("""
            CREATE INDEX IF NOT EXISTS idx_user_profiles_id ON user_profiles(id);
        """)
        
        print("   ✅ Index créé")
        
        # Créer une fonction de mise à jour automatique du timestamp
        await conn.execute("""
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = NOW();
                RETURN NEW;
            END;
            $$ language 'plpgsql';
        """)
        
        # Créer le trigger pour la mise à jour automatique
        await conn.execute("""
            DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
            CREATE TRIGGER update_user_profiles_updated_at
                BEFORE UPDATE ON user_profiles
                FOR EACH ROW
                EXECUTE FUNCTION update_updated_at_column();
        """)
        
        print("   ✅ Trigger de mise à jour créé")
        
        # Vérifier que la table existe
        result = await conn.fetchval("""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_name = 'user_profiles'
        """)
        
        if result > 0:
            print("   ✅ Table user_profiles confirmée")
            return True
        else:
            print("   ❌ Erreur : Table non créée")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors de la création de la table: {e}")
        return False
    finally:
        await conn.close()


async def test_user_profiles_table():
    """Tester la table user_profiles"""
    print("\n🧪 Test de la table user_profiles...")
    
    conn = await asyncpg.connect(DATABASE_URL, statement_cache_size=0)
    
    try:
        # Test d'insertion
        test_id = "550e8400-e29b-41d4-a716-************"  # UUID de test
        
        await conn.execute("""
            INSERT INTO user_profiles (id, first_name, last_name, company, role)
            VALUES ($1, $2, $3, $4, $5)
            ON CONFLICT (id) DO UPDATE SET
                first_name = EXCLUDED.first_name,
                updated_at = NOW()
        """, test_id, "Test", "User", "Test Company", "user")
        
        # Test de lecture
        result = await conn.fetchrow("""
            SELECT * FROM user_profiles WHERE id = $1
        """, test_id)
        
        if result:
            print("   ✅ Insertion/lecture réussie")
            print(f"      - Nom: {result['first_name']} {result['last_name']}")
            print(f"      - Entreprise: {result['company']}")
            print(f"      - Rôle: {result['role']}")
            
            # Nettoyer le test
            await conn.execute("DELETE FROM user_profiles WHERE id = $1", test_id)
            print("   ✅ Données de test nettoyées")
            
            return True
        else:
            print("   ❌ Échec du test")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur lors du test: {e}")
        return False
    finally:
        await conn.close()


async def main():
    """Fonction principale"""
    print("🚀 ORBIS - Configuration Table user_profiles")
    print("="*50)
    
    # Créer la table
    table_created = await create_user_profiles_table()
    
    if table_created:
        # Tester la table
        test_passed = await test_user_profiles_table()
        
        if test_passed:
            print("\n🎉 Configuration terminée avec succès!")
            print("✅ La table user_profiles est prête pour Supabase Auth")
            print("\n💡 Vous pouvez maintenant tester l'authentification:")
            print("   - Inscription: POST /api/v1/auth/register")
            print("   - Connexion: POST /api/v1/auth/login")
            return True
    
    print("\n❌ Échec de la configuration")
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
