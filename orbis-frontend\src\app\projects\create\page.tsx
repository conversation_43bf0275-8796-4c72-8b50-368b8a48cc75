'use client'

import { useState } from 'react'
import { ProjectForm } from '@/components/projects/ProjectForm'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function CreateProject() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (projectData: any) => {
    setIsSubmitting(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      console.log('Nouveau projet créé:', projectData)
      
      // Redirect to projects list
      router.push('/projects')
    } catch (error) {
      console.error('Erreur lors de la création du projet:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Nouveau Projet</h1>
          <p className="text-gray-600 mt-1">Créez un nouveau projet de construction</p>
        </div>
        <Link href="/projects">
          <Button variant="outline">
            Retour aux Projets
          </Button>
        </Link>
      </div>

      {/* Form */}
      <Card className="p-6">
        <ProjectForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          submitButtonText="Créer le Projet"
        />
      </Card>

      {/* Help Section */}
      <Card className="p-6 bg-blue-50 border-blue-200">
        <h2 className="text-lg font-semibold text-blue-900 mb-3">💡 Conseils pour créer un projet</h2>
        <ul className="space-y-2 text-blue-800">
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Définissez clairement les objectifs et la portée du projet</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Assignez un chef de projet expérimenté</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Établissez un budget réaliste avec une marge de sécurité</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Planifiez les étapes importantes et les livrables</span>
          </li>
          <li className="flex items-start">
            <span className="mr-2">•</span>
            <span>Identifiez les risques potentiels dès le début</span>
          </li>
        </ul>
      </Card>
    </div>
  )
}