
-- =====================================================
-- ORBIS Suivi Travaux - Schéma de base de données
-- Généré le: 2025-06-17 02:04:08
-- URL Supabase: https://dkmyxkkokwuxopahokcd.supabase.co
-- =====================================================

-- Activer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. TABLE COMPANIES (Base multi-tenant)
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    email VARCHAR(255),
    siret VARCHAR(50) UNIQUE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. TABLE USER_PROFILES (Profils utilisateurs)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(50),
    role VARCHAR(50) DEFAULT 'employee',
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. TABLE PROJECTS (Projets de construction)
CREATE TABLE IF NOT EXISTS public.projects (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    address TEXT,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'draft',
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    created_by UUID REFERENCES public.user_profiles(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. TABLE EMPLOYEES (Employés)
CREATE TABLE IF NOT EXISTS public.employees (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_number VARCHAR(50),
    user_profile_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    position VARCHAR(100),
    hourly_rate DECIMAL(10,2),
    hire_date DATE,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. TABLE TIME_ENTRIES (Suivi du temps)
CREATE TABLE IF NOT EXISTS public.time_entries (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    employee_id UUID REFERENCES public.employees(id) ON DELETE CASCADE,
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    hours_worked DECIMAL(5,2),
    description TEXT,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6. TABLE DOCUMENTS (Gestion des documents)
CREATE TABLE IF NOT EXISTS public.documents (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500),
    file_size INTEGER,
    mime_type VARCHAR(100),
    project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
    uploaded_by UUID REFERENCES public.user_profiles(id),
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- =====================================================
-- SÉCURITÉ: Activation Row Level Security (RLS)
-- =====================================================
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- INDEX POUR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_companies_name ON public.companies(name);
CREATE INDEX IF NOT EXISTS idx_companies_siret ON public.companies(siret);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_company_id ON public.user_profiles(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_company_id ON public.projects(company_id);
CREATE INDEX IF NOT EXISTS idx_projects_status ON public.projects(status);
CREATE INDEX IF NOT EXISTS idx_employees_company_id ON public.employees(company_id);
CREATE INDEX IF NOT EXISTS idx_employees_number ON public.employees(employee_number);
CREATE INDEX IF NOT EXISTS idx_time_entries_company_id ON public.time_entries(company_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_date ON public.time_entries(date);
CREATE INDEX IF NOT EXISTS idx_time_entries_employee_id ON public.time_entries(employee_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_project_id ON public.time_entries(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_project_id ON public.documents(project_id);
CREATE INDEX IF NOT EXISTS idx_documents_company_id ON public.documents(company_id);

-- =====================================================
-- DONNÉES D'EXEMPLE
-- =====================================================
INSERT INTO public.companies (name, address, phone, email, siret) VALUES
('ORBIS Construction', '123 Rue de la Paix, Paris 75001', '+33 1 23 45 67 89', '<EMAIL>', '12345678901234')
ON CONFLICT (siret) DO NOTHING;

-- Insertion des données liées avec gestion des UUID
DO $$
DECLARE
    company_uuid UUID;
    user_uuid UUID;
    project_uuid UUID;
    employee_uuid UUID;
BEGIN
    -- Récupérer l'UUID de l'entreprise
    SELECT id INTO company_uuid FROM public.companies WHERE siret = '12345678901234';
    
    IF company_uuid IS NOT NULL THEN
        -- Insérer un utilisateur admin
        INSERT INTO public.user_profiles (email, first_name, last_name, role, company_id) VALUES
        ('<EMAIL>', 'Jean', 'Dupont', 'admin', company_uuid)
        ON CONFLICT (email) DO NOTHING;
        
        -- Récupérer l'UUID de l'utilisateur
        SELECT id INTO user_uuid FROM public.user_profiles WHERE email = '<EMAIL>';
        
        IF user_uuid IS NOT NULL THEN
            -- Insérer un projet exemple
            INSERT INTO public.projects (name, description, address, start_date, budget, status, company_id, created_by) VALUES
            ('Projet Résidentiel Paris 15', 'Construction d''un immeuble résidentiel de 50 logements', '15 Avenue de la République, Paris 75015', CURRENT_DATE, 500000.00, 'active', company_uuid, user_uuid)
            ON CONFLICT DO NOTHING;
            
            -- Insérer un employé
            INSERT INTO public.employees (employee_number, user_profile_id, position, hourly_rate, hire_date, company_id) VALUES
            ('EMP001', user_uuid, 'Chef de chantier', 35.00, CURRENT_DATE, company_uuid)
            ON CONFLICT DO NOTHING;
        END IF;
    END IF;
END $$;

-- =====================================================
-- POLITIQUES RLS (Row Level Security)
-- =====================================================

-- Politique pour companies
CREATE POLICY "Companies visibility" ON public.companies
    FOR ALL USING (
        id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- Politique pour user_profiles
CREATE POLICY "User profiles visibility" ON public.user_profiles
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- Politique pour projects
CREATE POLICY "Projects visibility" ON public.projects
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- Politique pour employees
CREATE POLICY "Employees visibility" ON public.employees
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- Politique pour time_entries
CREATE POLICY "Time entries visibility" ON public.time_entries
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- Politique pour documents
CREATE POLICY "Documents visibility" ON public.documents
    FOR ALL USING (
        company_id IN (
            SELECT company_id FROM public.user_profiles 
            WHERE id = auth.uid()
        )
    );

-- =====================================================
-- VÉRIFICATIONS FINALES
-- =====================================================
-- Vérifier que toutes les tables ont été créées
SELECT 
    schemaname, 
    tablename,
    tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('companies', 'user_profiles', 'projects', 'employees', 'time_entries', 'documents')
ORDER BY tablename;

-- Compter les enregistrements d'exemple
SELECT 
    'companies' as table_name, COUNT(*) as records FROM public.companies
UNION ALL
SELECT 
    'user_profiles' as table_name, COUNT(*) as records FROM public.user_profiles
UNION ALL
SELECT 
    'projects' as table_name, COUNT(*) as records FROM public.projects;
